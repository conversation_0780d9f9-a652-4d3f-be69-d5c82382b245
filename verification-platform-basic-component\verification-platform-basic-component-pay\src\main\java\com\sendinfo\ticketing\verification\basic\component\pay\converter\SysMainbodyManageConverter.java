package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.SysMainbodyManageMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 经营主体支付配置转换器
 * 实现各种对象间的转换逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyManageConverter")
public class SysMainbodyManageConverter implements
        CreateParam2DoConverter<SysMainbodyManageCreateParam, SysMainbodyManageDO>,
        ReadDo2ModelConverter<SysMainbodyManageDO, SysMainbodyManage>,
        ReadParam2ArgConverter<SysMainbodyManageQueryParam, SysMainbodyManageQueryArg>,
        UpdateParam2ArgConverter<SysMainbodyManageUpdateParam, SysMainbodyManageUpdateArg, SysMainbodyManage> {

    private final SysMainbodyManageMapper mapper = SysMainbodyManageMapper.INSTANCE;

    @Override
    public SysMainbodyManageDO c_p2d(SysMainbodyManageCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        SysMainbodyManageDO dataObject = mapper.convert(createParam);
        // 可以在此添加MapStruct难以处理的额外逻辑
        return dataObject;
    }

    @Override
    public SysMainbodyManage r_d2m(SysMainbodyManageDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<SysMainbodyManage> r_ds2ms(List<SysMainbodyManageDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public SysMainbodyManageQueryArg r_p2a(SysMainbodyManageQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        SysMainbodyManageQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }

    @Override
    public SysMainbodyManageUpdateArg u_p2a(SysMainbodyManageUpdateParam updateParam, SysMainbodyManage currentModel) {
        if (updateParam == null) {
            return null;
        }
        SysMainbodyManageUpdateArg updateArg = mapper.convert(updateParam);
        Optional.ofNullable(updateParam.getUseFlag())
                .ifPresent(innerStatus -> updateArg.setUseFlagUpdater(StatusUpdater.transfer(currentModel.getUseFlag().getCode(), innerStatus.getCode())));
        return updateArg;
    }
} 