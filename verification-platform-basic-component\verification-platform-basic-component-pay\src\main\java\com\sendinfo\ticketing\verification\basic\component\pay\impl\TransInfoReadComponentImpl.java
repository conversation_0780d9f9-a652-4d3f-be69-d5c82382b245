package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.TransInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TransInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TransInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 交易记录读取组件实现
 *
 * @since 1.0.0
 */
@Component("transInfoReadComponent")
@Getter
public class TransInfoReadComponentImpl implements
        TransInfoReadComponent,
        DaoBasedSingleRead<Long, TransInfoQueryParam, TransInfo, TransInfoDO>,
        DaoBasedListRead<Long, TransInfoQueryParam, TransInfo, TransInfoDO, TransInfoQueryArg>,
        DaoBasedCountRead<Long, TransInfoQueryParam, TransInfo, TransInfoQueryArg> {

    private final TransInfoDao dao;
    private final TransInfoConverter converter;

    public TransInfoReadComponentImpl(TransInfoDao dao, TransInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TransInfo> queryTransInfoList(String orderCode, String corpCode) {
        List<TransInfoDO> transInfoDOList = dao.queryListByTradeCode(orderCode, corpCode);

        return converter.r_ds2ms(transInfoDOList);
    }

    @Override
    public List<TransInfo> queryListByTradeCodeAndTransType(String tradeCode, Integer transType, String corpCode) {
        return converter.r_ds2ms(dao.queryListByTradeCodeAndTransType(tradeCode, transType, corpCode));
    }
}