package com.sendinfo.ticketing.verification.basic.component.customer.converter;

import com.sendinfo.ticketing.verification.basic.component.customer.mapper.SalesmanRelationTravelMapper;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanRelationTravelQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanRelationTravel;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanRelationTravelQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.SalesmanRelationTravelDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

/**
 * 业务员关联客户转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("salesmanRelationTravelConverter")
public class SalesmanRelationTravelConverter implements ReadDo2ModelConverter<SalesmanRelationTravelDO, SalesmanRelationTravel>,
        ReadParam2ArgConverter<SalesmanRelationTravelQueryParam, SalesmanRelationTravelQueryArg> {

    private final SalesmanRelationTravelMapper mapper = SalesmanRelationTravelMapper.INSTANCE;

    // DO -> Model
    @Override
    public SalesmanRelationTravel r_d2m(SalesmanRelationTravelDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }


    // QueryParam -> QueryArg
    @Override
    public SalesmanRelationTravelQueryArg r_p2a(SalesmanRelationTravelQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        SalesmanRelationTravelQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }
} 