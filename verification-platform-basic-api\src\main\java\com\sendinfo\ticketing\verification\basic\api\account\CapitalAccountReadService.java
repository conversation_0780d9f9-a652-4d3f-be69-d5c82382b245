package com.sendinfo.ticketing.verification.basic.api.account;

import com.sendinfo.ticketing.verification.basic.api.account.request.CapitalAccountQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 资金账户只读服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CapitalAccountReadService {

    /**
     * 模糊查询资金账户列表
     *
     * @param request 查询条件
     * @return 资金账户列表
     */
    ResultModel<List<CapitalAccount>> queryAccountList(CapitalAccountQueryRequest request);
} 