package com.sendinfo.ticketing.verification.basic.component.customer.mapper;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoDeptQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoDeptQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelInfoDeptQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelInfoRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 旅行社信息对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface TravelInfoMapper {

    TravelInfoMapper INSTANCE = Mappers.getMapper(TravelInfoMapper.class);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    TravelInfoQueryArg convert(TravelInfoQueryParam queryParam);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    TravelInfoRoleQueryArg convert(TravelInfoRoleQueryParam queryParam);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    TravelInfoDeptQueryArg convert(TravelInfoDeptQueryParam queryParam);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryRequest 查询参数
     * @return 查询参数
     */
    TravelInfoQueryParam convert(TravelInfoQueryRequest queryRequest);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryRequest 查询参数
     * @return 查询参数
     */
    TravelInfoRoleQueryParam convert(TravelInfoRoleQueryRequest queryRequest);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryRequest 查询参数
     * @return 查询参数
     */
    TravelInfoDeptQueryParam convert(TravelInfoDeptQueryRequest queryRequest);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    TravelInfo convert(TravelInfoDO dataObject);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam   创建参数
     * @return  TravelInfoDO
     */
    TravelInfoDO convert(TravelInfoCreateParam createParam);

    /**
     * 创建参数转换为数据对象
     * @param createRequest 创建对象
     * @return  TravelInfoCreateParam
     */
    TravelInfoCreateParam convert(TravelInfoCreateRequest createRequest);
} 