package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.PwSysFunctionsReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwSysFunctionsConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysFunctionsQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysFunctionsDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysFunctionsDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 系统功能模块查询组件实现
 * 
 * <AUTHOR> 2025-07-24
 */
@Component("pwSysFunctionsReadComponent")
@Getter
public class PwSysFunctionsReadComponentImpl
        implements PwSysFunctionsReadComponent,
        DaoBasedPageRead<Long, PwSysFunctionsQueryParam, PwSysFunctions, PwSysFunctionsDO, PwSysFunctionsQueryArg>,
        DaoBasedSingleRead<Long, PwSysFunctionsQueryParam, PwSysFunctions, PwSysFunctionsDO>,
        DaoBasedCountRead<Long, PwSysFunctionsQueryParam, PwSysFunctions, PwSysFunctionsQueryArg> {

    private final PwSysFunctionsDao dao;
    private final PwSysFunctionsConverter converter;

    public PwSysFunctionsReadComponentImpl(PwSysFunctionsDao dao, PwSysFunctionsConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<PwSysFunctions> queryBySubSystemId(Integer subsystemId) {
        List<PwSysFunctionsDO> dataObjects = dao.queryBySubSystemId(subsystemId);
        return converter.r_ds2ms(dataObjects);
    }

    @Override
    public List<PwSysFunctions> queryByParentIds(List<Long> parentIds) {
        List<PwSysFunctionsDO> dataObjects = dao.queryByParentIds(parentIds);
        return converter.r_ds2ms(dataObjects);
    }

    @Override
    public List<PwSysFunctions> queryHasRequisitionList(String corpCode) {
        List<PwSysFunctionsDO> dataObjects = dao.queryHasRequisitionList(corpCode);
        return converter.r_ds2ms(dataObjects);
    }
}
