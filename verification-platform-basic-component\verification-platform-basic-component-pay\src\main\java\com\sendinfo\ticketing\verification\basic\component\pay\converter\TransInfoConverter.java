package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.api.pay.response.TransInfoPayAwayResponse;
import com.sendinfo.ticketing.verification.basic.component.pay.mapper.TransInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 交易记录对象转换器
 * 协调MapStruct Mapper，处理复杂转换逻辑
 *
 * @since 1.0.0
 */
@Component("transInfoConverter")
public class TransInfoConverter implements
        CreateParam2DoConverter<TransInfoCreateParam, TransInfoDO>,
        ReadDo2ModelConverter<TransInfoDO, TransInfo>,
        ReadParam2ArgConverter<TransInfoQueryParam, TransInfoQueryArg>,
        UpdateParam2ArgConverter<TransInfoUpdateParam, TransInfoUpdateArg, TransInfo>,
        DeleteParam2ArgConverter<TransInfoDeleteParam, TransInfoDeleteArg> {

    private final TransInfoMapper mapper = TransInfoMapper.INSTANCE;

    @Override
    public TransInfoDO c_p2d(TransInfoCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        return mapper.convert(createParam);
    }

    @Override
    public TransInfo r_d2m(TransInfoDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public TransInfoQueryArg r_p2a(TransInfoQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        TransInfoQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }

    @Override
    public TransInfoUpdateArg u_p2a(TransInfoUpdateParam updateParam, TransInfo currentModel) {
        if (updateParam == null) {
            return null;
        }
        TransInfoUpdateArg updateArg = mapper.convert(updateParam);

        Optional.ofNullable(updateParam.getPayStatus())
                .ifPresent(innerStatus -> updateArg.setPayStatusUpdater(StatusUpdater.transfer(currentModel.getPayStatus().getCode(), innerStatus.getCode())));
        return updateArg;
    }

    @Override
    public TransInfoDeleteArg d_p2a(TransInfoDeleteParam deleteParam) {
        return mapper.convert(deleteParam);
    }

    public TransInfoPayAwayResponse r_m2r(TransInfo transInfo) {
        return mapper.convert(transInfo);
    }
}