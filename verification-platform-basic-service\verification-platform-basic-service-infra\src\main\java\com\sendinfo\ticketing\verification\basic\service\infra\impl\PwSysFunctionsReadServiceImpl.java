/**
 *  系统功能模块查询服务实现
 *
 *  <AUTHOR> 2025-07-25
 */
package com.sendinfo.ticketing.verification.basic.service.infra.impl;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwSysFunctionsQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.PwSysFunctionsReadService;
import com.sendinfo.ticketing.verification.basic.component.infra.PwSysFunctionsReadComponent;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.PwSysFunctionsAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.infra.function.QueryPwSysFunctionsFunction;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 系统功能模块查询服务实现
 *
 * <AUTHOR> 2025-07-25
 */
@Service("pwSysFunctionsReadService")
@Slf4j
public class PwSysFunctionsReadServiceImpl implements PwSysFunctionsReadService {

    private final FlowAgentBuilder flowAgentBuilder;
    private final QueryPwSysFunctionsFunction queryPwSysFunctionsFunction;
    private final PwSysFunctionsReadComponent pwSysFunctionsReadComponent;

    public PwSysFunctionsReadServiceImpl(FlowAgentBuilder flowAgentBuilder,
            QueryPwSysFunctionsFunction queryPwSysFunctionsFunction,
            PwSysFunctionsReadComponent pwSysFunctionsReadComponent) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryPwSysFunctionsFunction = queryPwSysFunctionsFunction;
        this.pwSysFunctionsReadComponent = pwSysFunctionsReadComponent;
    }

    @Override
    public PageResultModel<PwSysFunctions> queryTreePageList(PageRequest<PwSysFunctionsQueryCondition> pageRequest) {
        return flowAgentBuilder
                .<PageRequest<PwSysFunctionsQueryCondition>, PageResultModel<PwSysFunctions>>validateThenChooseBuilder()
                .appendLogicAction(queryPwSysFunctionsFunction::queryTreePageList)
                .withSuccessfulAction(q -> {
                    List<PwSysFunctions> data = q
                            .getAttachment(PwSysFunctionsAttachmentKey.PW_SYS_FUNCTIONS_DATA_LIST_ATTACHMENT_KEY);
                    int total = q.getAttachment(PwSysFunctionsAttachmentKey.PW_SYS_FUNCTIONS_DATA_COUNT_ATTACHMENT_KEY);
                    return Results.successPage(data, total, pageRequest.getPageSize(), pageRequest.getCurrentPage());
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToPageResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[PwSysFunctionsReadServiceImpl] queryTreePageList error, request: {}", pageRequest, th);
                    return Results.failPage(InfraErrorDef.QUERY_SYS_FUNCTIONS_ERROR);
                })
                .rethrowException(VerificationBizRuntimeException.class::isInstance)
                .build()
                .prompt(pageRequest)
                .getResult();
    }

    /**
     * 查询已征用的功能模块列表
     *
     * @param corpCode 公司代码
     * @return 已征用的功能模块列表
     */
    @Override
    public ResultModel<List<PwSysFunctions>> queryHasRequisitionList(String corpCode) {
        return flowAgentBuilder.<String, ResultModel<List<PwSysFunctions>>>validateThenChooseBuilder()
                .appendLogicAction(queryPwSysFunctionsFunction::queryHasRequisitionTreeList)
                .withSuccessfulAction(q -> {
                    List<PwSysFunctions> data = q
                            .getAttachment(PwSysFunctionsAttachmentKey.PW_SYS_FUNCTIONS_TREE_LIST_ATTACHMENT_KEY);
                    return Results.success(data);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[PwSysFunctionsReadServiceImpl] queryHasRequisitionList error", th);
                    return Results.fail(InfraErrorDef.QUERY_SYS_FUNCTIONS_ERROR);
                })
                .rethrowException(VerificationBizRuntimeException.class::isInstance)
                .build()
                .prompt(corpCode)
                .getResult();
    }
}
