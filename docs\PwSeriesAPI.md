# Pw系列 API 文档

## 涉及类的完整路径

### 接口类
- `com.sendinfo.ticketing.verification.basic.api.infra.PwClientMenuReadService`
- `com.sendinfo.ticketing.verification.basic.api.infra.PwSysFunctionsReadService`
- `com.sendinfo.ticketing.verification.basic.api.infra.PwSysApplyTypeReadService`
- `com.sendinfo.ticketing.verification.basic.api.infra.PwSysSubsystemReadService`
- `com.sendinfo.ticketing.verification.basic.api.infra.PwSysFunctionManageService`
- `com.sendinfo.ticketing.verification.basic.api.infra.PwClientMenuManageService`

### 模型类
- `com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu`
- `com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions`
- `com.sendinfo.ticketing.verification.basic.model.infra.PwSysApplyType`
- `com.sendinfo.ticketing.verification.basic.model.infra.PwSysSubsystem`
- `com.sendinfo.ticketing.verification.basic.model.TenantVOBase`

### 请求条件类
- `com.sendinfo.ticketing.verification.basic.api.infra.request.PwClientMenuQueryCondition`
- `com.sendinfo.ticketing.verification.basic.api.infra.request.PwSysFunctionsQueryCondition`

### 通用类
- `com.sendinfo.ticketing.verification.common.api.request.PageRequest`
- `com.sendinfo.ticketing.verification.common.api.result.PageResultModel`
- `com.sendinfo.ticketing.verification.common.api.result.ResultModel`

---

## 1. 客户端菜单查询

**接口类**: `com.sendinfo.ticketing.verification.basic.api.infra.PwClientMenuReadService`

### 接口方法 queryTreePageList

```java
/**
 * 分页查询客户端菜单
 *
 * @param pageRequest 分页查询条件
 * @return 分页结果
 */
PageResultModel<PwClientMenu> queryTreePageList(PageRequest<PwClientMenuQueryCondition> pageRequest);
```

#### 入参

**参数类型**: `PageRequest<PwClientMenuQueryCondition>`

**PwClientMenuQueryCondition 属性说明**:
- `corpCode` (String): 企业编码
- `businessType` (Integer): 业务类型：0:门票1:剧院2:滑雪场3:一卡通
- `menuName` (String): 菜单名称
- `menuCode` (String): 菜单编码
- `functionType` (String): 功能类别 menu（菜单）、column（栏目）、button（按钮）、TAB（标签栏）
- `subsystemId` (Integer): 子系统ID

**PageRequest 属性说明**:
- `currentPage` (Integer): 当前页码
- `pageSize` (Integer): 每页大小
- `condition` (PwClientMenuQueryCondition): 查询条件

#### 返回

**返回类型**: `PageResultModel<PwClientMenu>`

**PwClientMenu 属性说明**:
- 继承自 `TenantVOBase` 的属性：
  - `id` (Long): 主键ID
  - `createTime` (Date): 创建时间
  - `modifyTime` (Date): 更新时间
  - `createBy` (String): 创建人
  - `modifyBy` (String): 更新人
  - `deleted` (String): 是否删除
  - `corpCode` (String): 企业编码
- 自有属性：
  - `businessType` (Integer): 业务类型：0:门票1:剧院2:滑雪场3:一卡通
  - `menuName` (String): 菜单名称
  - `menuCode` (String): 菜单编码
  - `iconImg` (String): 栏目图标URL地址
  - `title` (String): 栏目标题
  - `menuUrl` (String): 栏目地址
  - `wpfUrl` (String): 栏目地址
  - `sysCode` (String): 插件名称
  - `sortOrder` (Integer): 序号从小到大
  - `status` (String): 状态 F:停用 T:启动
  - `remark` (String): 备注
  - `parentId` (Integer): 父ID
  - `parentIds` (String): 父IDS，逗号分隔
  - `functionType` (String): 功能类别 menu（菜单）、column（栏目）、button（按钮）、TAB（标签栏）
  - `subsystemId` (Integer): 子系统ID
  - `styleFileUrl` (String): 样式文件地址
  - `requisitionFlag` (Integer): 新系统征用标记, 1标识已征用；0标识未征用
  - `children` (List<PwClientMenu>): 子菜单列表

### 接口方法 queryHasRequisitionList

```java
/**
 * 查询已征用的菜单列表
 *
 * @param corpCode 公司代码
 * @return 已征用的菜单列表
 */
ResultModel<List<PwClientMenu>> queryHasRequisitionList(String corpCode);
```

#### 入参

**参数类型**: `String`

**参数说明**:
- `corpCode`: 公司代码，用于过滤特定公司的已征用菜单

#### 返回

**返回类型**: `ResultModel<List<PwClientMenu>>`

**说明**: 返回的PwClientMenu对象属性同上，包含完整的树形结构数据

---

## 2. 系统功能查询

**接口类**: `com.sendinfo.ticketing.verification.basic.api.infra.PwSysFunctionsReadService`

### 接口方法 queryTreePageList

```java
/**
 * 分页查询系统功能模块
 *
 * @param pageRequest 分页查询条件
 * @return 分页结果
 */
PageResultModel<PwSysFunctions> queryTreePageList(PageRequest<PwSysFunctionsQueryCondition> pageRequest);
```

#### 入参

**参数类型**: `PageRequest<PwSysFunctionsQueryCondition>`

**PwSysFunctionsQueryCondition 属性说明**:
- `corpCode` (String): 企业编码
- `funName` (String): 名称
- `funCode` (String): 唯一编码
- `menuCode` (String): 菜单编码
- `functionType` (String): 功能类别 menu（菜单）、column（栏目）、button（按钮）
- `subsystemId` (Integer): 子系统ID

**PageRequest 属性说明**:
- `currentPage` (Integer): 当前页码
- `pageSize` (Integer): 每页大小
- `condition` (PwSysFunctionsQueryCondition): 查询条件

#### 返回

**返回类型**: `PageResultModel<PwSysFunctions>`

**PwSysFunctions 属性说明**:
- 继承自 `TenantVOBase` 的属性：
  - `id` (Long): 主键ID
  - `createTime` (Date): 创建时间
  - `modifyTime` (Date): 更新时间
  - `createBy` (String): 创建人
  - `modifyBy` (String): 更新人
  - `deleted` (String): 是否删除
  - `corpCode` (String): 企业编码
- 自有属性：
  - `funName` (String): 名称
  - `funCode` (String): 唯一编码
  - `menuCode` (String): 菜单编码
  - `funIco` (String): 功能图标
  - `funUrl` (String): URL
  - `funType` (String): 0:平台管理员 1:供应商 2:代理商 3:分销商 多个用豆号隔开
  - `optType` (String): 名称:标识，名称:标识
  - `parentId` (Integer): 父ID
  - `parentIds` (String): 父IDS，逗号分隔
  - `sortNo` (Integer): 排序
  - `hierarchy` (Short): 层级深度
  - `openType` (String): 打开方式：_self(当前页打开)、_blank(新页面打开)
  - `functionType` (String): 功能类别 menu（菜单）、column（栏目）、button（按钮）
  - `outerChain` (String): 外链
  - `subsystemId` (Integer): 子系统ID
  - `cloudFunUrl` (String): 微服务文件地址
  - `useFlag` (String): 禁启用标识
  - `description` (String): 页面说明
  - `isGroupShow` (Integer): 集团是否展示（1是展示，2不展示）
  - `requisitionFlag` (Integer): 新系统征用标记, 1标识已征用；0标识未征用
  - `children` (List<PwSysFunctions>): 子功能模块列表

### 接口方法 queryHasRequisitionList

```java
/**
 * 查询有需求功能的功能模块列表
 *
 * @return 功能模块列表
 */
ResultModel<List<PwSysFunctions>> queryHasRequisitionList();
```

#### 入参

无参数

#### 返回

**返回类型**: `ResultModel<List<PwSysFunctions>>`

**说明**: 返回的PwSysFunctions对象属性同上，包含完整的树形结构数据

---

## 3. 系统应用类型查询

**接口类**: `com.sendinfo.ticketing.verification.basic.api.infra.PwSysApplyTypeReadService`

### 接口方法 queryAllList

```java
/**
     * 根据子系统ID查询应用类型列表
     * 只返回启用且未删除的记录
     * 
     * @param subsystemId 子系统ID
     * @return 应用类型列表
     */
    ResultModel<List<PwSysApplyType>> queryBySubSystemId(Integer subsystemId);
```

#### 入参

**参数类型**: `Integer subsystemId` - 子系统ID

#### 返回

**返回类型**: `ResultModel<List<PwSysApplyType>>`

**PwSysApplyType 属性说明**:
- `id` (Long): ID
- `corpCode` (String): 企业编码
- `subsystemId` (Integer): 子系统ID
- `modelType` (Character): 应用类型 1系统后台2窗口 3手持机 4自助机
- `modelCode` (String): 应用编码
- `modelName` (String): 应用名称
- `useFlag` (Character): 是否启用T:启用F：禁用
- `createBy` (String): 创建人
- `createTime` (Date): 创建时间
- `modifyBy` (String): 修改人
- `modifyTime` (Date): 修改时间
- `deleted` (Character): 删除标志F未删除T已删除

---

## 4. 系统子系统查询

**接口类**: `com.sendinfo.ticketing.verification.basic.api.infra.PwSysSubsystemReadService`


### 接口方法 queryAllList

```java
/**
 * 查询所有系统子系统列表
 *
 * @return 子系统列表
 */
ResultModel<List<PwSysSubsystem>> queryAllList();
```

#### 入参

无参数

#### 返回

**返回类型**: `ResultModel<List<PwSysSubsystem>>`

**PwSysSubsystem 属性说明**:
- `id` (Long): ID
- `corpCode` (String): 企业编码
- `applyModel` (String): 模块名称
- `applyName` (String): 应用名称
- `applyDesc` (String): 功能描述
- `useFlag` (String): 是否启用T:启用F：禁用
- `sortNo` (Integer): 排序
- `createBy` (String): 创建人
- `createTime` (LocalDateTime): 创建时间
- `modifyBy` (String): 修改人
- `modifyTime` (LocalDateTime): 修改时间
- `deleted` (String): 删除标志F未删除T已删除
- `applyCode` (String): 模块编码

---

## 5. 系统功能管理

**接口类**: `com.sendinfo.ticketing.verification.basic.api.infra.PwSysFunctionManageService`

### 接口方法 markAsRequisition

```java
/**
 * 标记系统功能为已征用
 *
 * @param sysFunctionIds 系统功能ID列表
 * @return 操作结果
 */
ResultModel<Boolean> markAsRequisition(List<Long> sysFunctionIds);
```

#### 入参

**参数类型**: `List<Long>` - 系统功能ID列表

**说明**: 传入需要标记为已征用的系统功能ID列表

#### 返回

**返回类型**: `ResultModel<Boolean>`

**说明**: 返回操作是否成功，true表示有记录被更新，false表示没有记录被更新

---

## 6. 客户端菜单管理

**接口类**: `com.sendinfo.ticketing.verification.basic.api.infra.PwClientMenuManageService`

### 接口方法 markAsRequisition

```java
/**
 * 标记客户端菜单为已征用
 *
 * @param clientMenuIds 客户端菜单ID列表
 * @return 操作结果
 */
ResultModel<Boolean> markAsRequisition(List<Long> clientMenuIds);
```

#### 入参

**参数类型**: `List<Long>` - 客户端菜单ID列表

**说明**: 传入需要标记为已征用的客户端菜单ID列表

#### 返回

**返回类型**: `ResultModel<Boolean>`

**说明**: 返回操作是否成功，true表示有记录被更新，false表示没有记录被更新

