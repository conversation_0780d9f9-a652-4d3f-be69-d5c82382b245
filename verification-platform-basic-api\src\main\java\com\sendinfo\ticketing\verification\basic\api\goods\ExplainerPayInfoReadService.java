package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerPayInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerPayInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 服务类目计费规则读取服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ExplainerPayInfoReadService {

    /**
     * 模糊查询服务类目计费规则列表
     *
     * @param request 查询条件
     * @return 计费规则列表
     */
    ResultModel<List<ExplainerPayInfo>> queryPayInfoList(ExplainerPayInfoQueryRequest request);
} 