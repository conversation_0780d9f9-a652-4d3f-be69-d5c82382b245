package com.sendinfo.ticketing.verification.basic.api.customer;

import com.sendinfo.ticketing.verification.basic.api.customer.request.GuideFuzzyQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.response.GuideSearchResponse;
import com.sendinfo.ticketing.verification.basic.model.customer.Guide;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 18:03
 */
public interface GuideReadService {

    /**
     * 模糊查询导游列表
     *
     * @param request 条件
     * @return 导游列表
     */
    ResultModel<List<Guide>> fuzzyQueryGuideList(GuideFuzzyQueryRequest request);

    /**
     * 搜索导游列表
     *
     * @param request   查询条件
     * @return  导游列表
     */
    ResultModel<List<GuideSearchResponse>> searchGuideList(GuideFuzzyQueryRequest request);
}
