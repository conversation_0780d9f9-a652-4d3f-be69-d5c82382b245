package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置子商户关联表创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageMmSubMerchantsCreateParam extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 经营主体管理ID
     * 关联经营主体管理表的主键
     */
    private Long mainbodyManageId;

    /**
     * 子商户ID
     * 关联子商户表的主键
     */
    private Long subMerchantsId;

    /**
     * 子商户名称
     * 子商户的显示名称
     */
    private String subMerchantsName;

    /**
     * 子商户号
     * 子商户的唯一标识号
     */
    private String merchantNo;

    /**
     * create by
     */
    private String createBy;

    /**
     * modify by
     */
    private String modifyBy;
} 