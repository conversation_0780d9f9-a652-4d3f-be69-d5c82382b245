/**
 * System apply type DAO test class
 *
 * <AUTHOR> 2025-07-25 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.alibaba.nacos.api.exception.NacosException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.infra.TestMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysApplyTypeUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl.PwSysApplyTypeDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysApplyTypeDO;

@SpringBootTest(classes = PwSysApplyTypeDaoTest.Config.class)
@EnableAutoConfiguration
@Rollback
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:test-application.properties")
public class PwSysApplyTypeDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private PwSysApplyTypeDao pwSysApplyTypeDao;

    private static final String TEST_CORP_CODE = "TEST_CORP";

    @Configuration
    @Import({ SaasPwMybatisConfig.class })
    static class Config {
        @Bean
        PwSysApplyTypeDao pwSysApplyTypeDao(@Qualifier(SaasPwMybatisConfig.BEAN_NAME_SQL_SESSION_TEMPLATE) SqlSessionTemplate sqlSessionTemplate) {
            return new PwSysApplyTypeDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    @Before
    public void setUp() {
        // 初始化测试数据 - 插入10条数据
        for (int i = 1; i <= 10; i++) {
            PwSysApplyTypeDO applyType = createTestApplyType(i);
            pwSysApplyTypeDao.insert(applyType);
        }
    }

    @Test
    public void testInsert() {
        // Given
        PwSysApplyTypeDO applyType = createTestApplyType(100);

        // When
        pwSysApplyTypeDao.insert(applyType);

        // Then
        PwSysApplyTypeDO found = pwSysApplyTypeDao.queryById(applyType.getId());
        Assert.assertNotNull(found);
        Assert.assertEquals(applyType.getModelCode(), found.getModelCode());
        Assert.assertEquals(applyType.getModelName(), found.getModelName());
        Assert.assertEquals(applyType.getSubsystemId(), found.getSubsystemId());
    }

    @Test
    public void testQueryById() {
        // Given
        PwSysApplyTypeDO applyType = createTestApplyType(200);
        pwSysApplyTypeDao.insert(applyType);

        // When
        PwSysApplyTypeDO found = pwSysApplyTypeDao.queryById(applyType.getId());

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(applyType.getId(), found.getId());
        Assert.assertEquals(applyType.getModelCode(), found.getModelCode());
        Assert.assertEquals(applyType.getModelName(), found.getModelName());
    }


    @Test
    public void testSoftDeleteByArg() {
        // Given
        PwSysApplyTypeDO applyType = createTestApplyType(400);
        pwSysApplyTypeDao.insert(applyType);

        PwSysApplyTypeDeleteArg deleteArg = new PwSysApplyTypeDeleteArg();
        deleteArg.setId(applyType.getId());
        deleteArg.setModifyBy("testUser");

        // When
        int result = pwSysApplyTypeDao.softDeleteByArg(deleteArg);

        // Then
        Assert.assertEquals(1, result);
        PwSysApplyTypeDO deleted = pwSysApplyTypeDao.queryById(applyType.getId());
        Assert.assertNull(deleted); // 软删除后查询不到
    }

    @Test
    public void testDeleteByArg() {
        // Given
        PwSysApplyTypeDO applyType = createTestApplyType(500);
        pwSysApplyTypeDao.insert(applyType);

        PwSysApplyTypeDeleteArg deleteArg = new PwSysApplyTypeDeleteArg();
        deleteArg.setId(applyType.getId());

        // When
        int result = pwSysApplyTypeDao.deleteByArg(deleteArg);

        // Then
        Assert.assertEquals(1, result);
    }

    @Test
    public void testCountByArg() {
        // Given
        PwSysApplyTypeQueryArg queryArg = new PwSysApplyTypeQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setSubsystemId(1);

        // When
        int count = pwSysApplyTypeDao.countByArg(queryArg);

        // Then
        Assert.assertTrue(count >= 1); // 至少有setUp中插入的数据
    }

    @Test
    public void testQueryByArg() {
        // Given
        PwSysApplyTypeQueryArg queryArg = new PwSysApplyTypeQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setSubsystemId(1);
        queryArg.setOffset(0);
        queryArg.setLimit(5);

        // When
        List<PwSysApplyTypeDO> results = pwSysApplyTypeDao.queryByArg(queryArg);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.size() <= 5);
        for (PwSysApplyTypeDO applyType : results) {
            Assert.assertEquals(TEST_CORP_CODE, applyType.getCorpCode());
            Assert.assertEquals("F", applyType.getDeleted());
        }
    }

    @Test
    public void testQueryByArgWithFilters() {
        // Given
        PwSysApplyTypeQueryArg queryArg = new PwSysApplyTypeQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setModelType('1');
        queryArg.setModelName("Test Apply Type");
        queryArg.setOffset(0);
        queryArg.setLimit(10);

        // When
        List<PwSysApplyTypeDO> results = pwSysApplyTypeDao.queryByArg(queryArg);

        // Then
        Assert.assertNotNull(results);
        for (PwSysApplyTypeDO applyType : results) {
            Assert.assertEquals(Character.valueOf('1'), applyType.getModelType());
            Assert.assertTrue(applyType.getModelName().contains("Test Apply Type"));
        }
    }

    @Test
    public void testQueryBySubSystemId() {
        // Given
        Integer subsystemId = 1;

        // When
        List<PwSysApplyTypeDO> results = pwSysApplyTypeDao.queryBySubSystemId(subsystemId);

        // Then
        Assert.assertNotNull(results);
        for (PwSysApplyTypeDO applyType : results) {
            Assert.assertEquals(subsystemId, applyType.getSubsystemId());
            Assert.assertEquals(Character.valueOf('T'), applyType.getUseFlag());
            Assert.assertEquals("F", applyType.getDeleted());
        }
    }

    @Test
    public void testQueryBySubSystemIdNotFound() {
        // Given
        Integer subsystemId = 999;

        // When
        List<PwSysApplyTypeDO> results = pwSysApplyTypeDao.queryBySubSystemId(subsystemId);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.isEmpty());
    }

    @Test
    public void testQueryByIdNotFound() {
        // When
        PwSysApplyTypeDO found = pwSysApplyTypeDao.queryById(99999L);

        // Then
        Assert.assertNull(found);
    }

    @Test
    public void testQueryByIdDeleted() {
        // Given
        PwSysApplyTypeDO applyType = createTestApplyType(700);
        applyType.setDeleted("T"); // 设置为已删除
        pwSysApplyTypeDao.insert(applyType);

        // When
        PwSysApplyTypeDO found = pwSysApplyTypeDao.queryById(applyType.getId());

        // Then
        Assert.assertNull(found); // 已删除的记录不应该被查询到
    }

    private PwSysApplyTypeDO createTestApplyType(int index) {
        PwSysApplyTypeDO applyType = new PwSysApplyTypeDO();
        applyType.setCorpCode(TEST_CORP_CODE);
        applyType.setSubsystemId(index % 3 + 1); // 轮换子系统ID 1,2,3
        applyType.setModelType('1');
        applyType.setModelCode("CODE_" + index);
        applyType.setModelName("Test Apply Type " + index);
        applyType.setUseFlag('T');
        applyType.setCreateBy("testUser");
        applyType.setModifyBy("testUser");
        applyType.setDeleted("F");
        return applyType;
    }
}