package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.model.goods.TicketCalendarPrice;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/28 9:23
 **/
public interface TicketCalendarPriceReadService {

    ResultModel<List<TicketCalendarPrice>> batchQueryByTicketIds(@NotEmpty List<Long> ticketIds, @NotNull String corpCode);
}
