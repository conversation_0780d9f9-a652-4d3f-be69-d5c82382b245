package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.PwClientMenuReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwClientMenuConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwClientMenuQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwClientMenuDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwClientMenuDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

/**
 * 客户端菜单查询组件实现
 * 
 * <AUTHOR> Generated 2025-07-25
 */
@Component("pwClientMenuReadComponent")
@Getter
public class PwClientMenuReadComponentImpl
        implements PwClientMenuReadComponent,
        DaoBasedPageRead<Long, PwClientMenuQueryParam, PwClientMenu, PwClientMenuDO, PwClientMenuQueryArg>,
        DaoBasedSingleRead<Long, PwClientMenuQueryParam, PwClientMenu, PwClientMenuDO>,
        DaoBasedCountRead<Long, PwClientMenuQueryParam, PwClientMenu, PwClientMenuQueryArg> {

    private final PwClientMenuDao dao;
    private final PwClientMenuConverter converter;

    public PwClientMenuReadComponentImpl(PwClientMenuDao dao, PwClientMenuConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<PwClientMenu> queryByParentIds(List<Long> parentIds) {
        List<PwClientMenuDO> dataObjects = dao.queryByParentIds(parentIds);
        return converter.r_ds2ms(dataObjects);
    }

    @Override
    public List<PwClientMenu> queryHasRequisitionList(String corpCode) {
        List<PwClientMenuDO> dataObjects = dao.queryHasRequisitionList(corpCode);
        return converter.r_ds2ms(dataObjects);
    }
}
