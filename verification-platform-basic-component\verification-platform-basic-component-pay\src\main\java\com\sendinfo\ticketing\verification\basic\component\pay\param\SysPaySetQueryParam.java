package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayDefaultEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;

/**
 * 支付标签查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class SysPaySetQueryParam extends AbstractPageQueryParam {

    /**
     * 企业码
     */
    private String corpCode;

    /**
     * 支付平台编码
     */
    private String payTypeCode;

    /**
     * 启用状态
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 支付类型（主扫，被扫，现金）
     */
    private PayTypeEnum payType;

    /**
     * 自定义支付名称（模糊查询）
     */
    private String payName;

    /**
     * 支付标签名称（模糊查询）
     */
    private String paylableName;

    /**
     * 售票员默认收款方式
     */
    private PayDefaultEnum payDefault;
} 