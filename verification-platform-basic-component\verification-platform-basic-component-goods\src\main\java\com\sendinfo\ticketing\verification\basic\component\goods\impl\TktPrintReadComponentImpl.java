package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktPrintReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktPrintConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktPrintQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktPrint;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktPrintQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktPrintDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktPrintDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 打印设置读取组件实现类
 * <AUTHOR>
 */
@Component("tktPrintReadComponent")
@Getter
public class TktPrintReadComponentImpl implements TktPrintReadComponent,
        DaoBasedPageRead<Long, TktPrintQueryParam, TktPrint, TktPrintDO, TktPrintQueryArg>,
        DaoBasedSingleRead<Long, TktPrintQueryParam, TktPrint, TktPrintDO>,
        DaoBasedCountRead<Long, TktPrintQueryParam, TktPrint, TktPrintQueryArg> {

    private final TktPrintDao dao;

    private final TktPrintConverter converter;

    public TktPrintReadComponentImpl(TktPrintDao dao, TktPrintConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public TktPrint getTktPrintByTicketCode(String corpCode, String ticketCode) {
        TktPrintDO tktPrintDO = dao.getTktPrintByTicketCode(corpCode, ticketCode);

        return Optional.ofNullable(tktPrintDO)
                .map(converter::r_d2m)
                .orElse(null);
    }

    @Override
    public List<TktPrint> batchQueryTktPrintByTicketIds(List<Long> ticketIds, String corpCode) {
        List<TktPrintDO> tktPrintDOS = dao.batchQueryTktPrintByTicketIds(ticketIds, corpCode);
        return converter.r_ds2ms(tktPrintDOS);
    }
}