package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.customer.request.GuideFuzzyQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.GuideReadService;
import com.sendinfo.ticketing.verification.basic.model.customer.Guide;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/14 10:02
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class GuideReadServiceImplTest {

    @Autowired
    private GuideReadService guideReadService;

//    @Test
    public void testFuzzyQueryGuideListSuccess() {
        GuideFuzzyQueryRequest request = new GuideFuzzyQueryRequest();

        request.setCorpCode("0001");
        request.setFuzzyName("测试");
        ResultModel<List<Guide>> resultModel = guideReadService.fuzzyQueryGuideList(request);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertFalse(resultModel.getModel().isEmpty());
    }

//    @Test
    public void testFuzzyQueryGuideListEmptySuccess() {
        GuideFuzzyQueryRequest request = new GuideFuzzyQueryRequest();

        request.setCorpCode("0001");
        request.setFuzzyName("测试12312323123");
        ResultModel<List<Guide>> resultModel = guideReadService.fuzzyQueryGuideList(request);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertTrue(resultModel.getModel().isEmpty());
    }
}
