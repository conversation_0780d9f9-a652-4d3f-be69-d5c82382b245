/**
 *  系统功能模块查询功能类
 *
 *  <AUTHOR> 2025-07-25
 */
package com.sendinfo.ticketing.verification.basic.service.infra.function;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwSysFunctionsQueryCondition;
import com.sendinfo.ticketing.verification.basic.component.infra.PwSysFunctionsReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwSysFunctionsConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysFunctionsQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.PwSysFunctionsAttachmentKey;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.service.function.QueryPageByConditionFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QueryPageByConditionLogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sendinfo.ticketing.verification.basic.service.infra.enums.PwSysFunctionsAttachmentKey.PW_SYS_FUNCTIONS_DATA_COUNT_ATTACHMENT_KEY;
import static com.sendinfo.ticketing.verification.basic.service.infra.enums.PwSysFunctionsAttachmentKey.PW_SYS_FUNCTIONS_DATA_LIST_ATTACHMENT_KEY;

/**
 * 系统功能模块查询功能类
 *
 * <AUTHOR> 2025-07-25
 */
@Getter
@Function("queryPwSysFunctionsFunction")
@Slf4j
public class QueryPwSysFunctionsFunction implements QueryPageByConditionFunction<PwSysFunctionsQueryCondition> {

    private final LogicAction<PageRequest<PwSysFunctionsQueryCondition>> queryPageByCondition;
    private final PwSysFunctionsReadComponent pwSysFunctionsReadComponent;
    private final PwSysFunctionsConverter converter;

    public QueryPwSysFunctionsFunction(PwSysFunctionsReadComponent pwSysFunctionsReadComponent,
            PwSysFunctionsConverter converter) {
        this.pwSysFunctionsReadComponent = pwSysFunctionsReadComponent;
        this.converter = converter;

        this.queryPageByCondition = new QueryPageByConditionLogicAction<>(
                pwSysFunctionsReadComponent,
                this.converter::r_pr2p,
                PW_SYS_FUNCTIONS_DATA_LIST_ATTACHMENT_KEY,
                PW_SYS_FUNCTIONS_DATA_COUNT_ATTACHMENT_KEY);
    }

    /**
     * 查询树形结构的功能模块分页列表，分页依据根节点，分页列表元素是根节点，用树形结构显示根节点下的所有子节点
     */
    public Hint queryTreePageList(Question<PageRequest<PwSysFunctionsQueryCondition>> question) {
        PageRequest<PwSysFunctionsQueryCondition> pageRequest = question.getBody();
        PwSysFunctionsQueryParam pwSysFunctionsQueryParam = this.converter.r_pr2p(pageRequest);

        int count = pwSysFunctionsReadComponent.count(pwSysFunctionsQueryParam);
        List<PwSysFunctions> rootFunctions = Collections.emptyList();
        if (count > 0) {
            // 只会查询到一级节点
            rootFunctions = pwSysFunctionsReadComponent.list(pwSysFunctionsQueryParam);
            buildFunctionTree(rootFunctions);
        }

        question.setAttachment(PW_SYS_FUNCTIONS_DATA_LIST_ATTACHMENT_KEY, rootFunctions);
        question.setAttachment(PW_SYS_FUNCTIONS_DATA_COUNT_ATTACHMENT_KEY, count);
        return Hint.gotoNext();
    }

    /**
     * 查询树形结构的已征用功能模块列表
     */
    public Hint queryHasRequisitionTreeList(Question<String> question) {
        // 获取公司代码
        String corpCode = question.getBody();

        // 调用组件方法获取所有已征用的节点
        List<PwSysFunctions> allRequisitionFunctions = pwSysFunctionsReadComponent.queryHasRequisitionList(corpCode);

        // 组装树结构
        List<PwSysFunctions> treeFunctions = buildFunctionTreeFromList(allRequisitionFunctions);

        question.setAttachment(PwSysFunctionsAttachmentKey.PW_SYS_FUNCTIONS_TREE_LIST_ATTACHMENT_KEY, treeFunctions);
        return Hint.gotoNext();
    }

    /**
     * 构建功能模块树结构（从根节点开始递归查询）
     */
    private List<PwSysFunctions> buildFunctionTree(List<PwSysFunctions> rootFunctions) {
        if (rootFunctions == null || rootFunctions.isEmpty()) {
            return new ArrayList<>();
        }
        for (PwSysFunctions function : rootFunctions) {
            loadChildren(function, 0);
        }

        return rootFunctions;
    }

    /**
     * 递归加载子节点
     */
    private void loadChildren(PwSysFunctions parentFunction, int level) {
        // 最多递归5级
        if (level >= 5) {
            return;
        }
        List<Long> parentIds = List.of(parentFunction.getId());
        List<PwSysFunctions> children = pwSysFunctionsReadComponent.queryByParentIds(parentIds);

        if (children != null && !children.isEmpty()) {
            parentFunction.setChildren(children);
            // 递归加载每个子节点的子节点
            for (PwSysFunctions child : children) {
                loadChildren(child, level + 1);
            }
        }
    }

    /**
     * 从扁平列表构建树结构
     */
    private List<PwSysFunctions> buildFunctionTreeFromList(List<PwSysFunctions> allFunctions) {
        if (allFunctions == null || allFunctions.isEmpty()) {
            return new ArrayList<>();
        }

        // 按ID建立索引
        Map<Long, PwSysFunctions> functionMap = allFunctions.stream()
                .collect(Collectors.toMap(PwSysFunctions::getId, function -> function));

        List<PwSysFunctions> rootFunctions = new ArrayList<>();

        for (PwSysFunctions function : allFunctions) {
            Integer parentId = function.getParentId();
            if (parentId == null || parentId == 0) {
                // 根节点
                rootFunctions.add(function);
            } else {
                // 子节点，找到父节点并添加到其children中
                PwSysFunctions parent = functionMap.get(parentId.longValue());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(function);
                }
            }
        }

        return rootFunctions;
    }
}
