package com.sendinfo.ticketing.verification.basic.api.customer;

import com.sendinfo.ticketing.verification.basic.api.customer.request.DirectorInfoQueryByTravelIdRequest;
import com.sendinfo.ticketing.verification.basic.model.customer.DirectorInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 主管信息读取服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface DirectorInfoReadService {

    /**
     * 根据旅行社ID查询启用的主管信息
     *
     * @param request 查询请求
     * @return 主管信息列表
     */
    ResultModel<List<DirectorInfo>> queryByTravelId(DirectorInfoQueryByTravelIdRequest request);
} 