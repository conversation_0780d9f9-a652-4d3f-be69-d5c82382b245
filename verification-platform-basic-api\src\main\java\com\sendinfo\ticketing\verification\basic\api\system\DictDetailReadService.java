package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.DictDetailBatchQueryByDictIdsRequest;
import com.sendinfo.ticketing.verification.basic.api.system.request.DictDetailQueryByDictIdRequest;
import com.sendinfo.ticketing.verification.basic.model.system.DictDetail;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 15:45
 */
public interface DictDetailReadService {

    /**
     * 查询字典明细列表
     *
     * @param request 条件
     * @return 字典明细列表
     */
    ResultModel<List<DictDetail>> queryDictDetailListByDictId(DictDetailQueryByDictIdRequest request);

    /**
     * 批量查询字典明细列表
     *
     * @param request 条件
     * @return 字典明细列表
     */
    ResultModel<List<DictDetail>> batchQueryDictDetailListByDictIds(DictDetailBatchQueryByDictIdsRequest request);
}
