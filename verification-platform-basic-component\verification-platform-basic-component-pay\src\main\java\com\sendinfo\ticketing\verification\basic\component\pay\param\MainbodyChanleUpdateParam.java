package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商户渠道配置更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MainbodyChanleUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付渠道ID
     */
    private Long payChanleId;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改人
     */
    private String modifyBy;
} 