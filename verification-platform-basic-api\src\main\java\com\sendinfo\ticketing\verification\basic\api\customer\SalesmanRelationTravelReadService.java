package com.sendinfo.ticketing.verification.basic.api.customer;

import com.sendinfo.ticketing.verification.basic.api.customer.request.SalesmanRelationTravelQueryByTravelIdRequest;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanRelationTravel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 业务员关联客户读取服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SalesmanRelationTravelReadService {

    /**
     * 根据客户ID查询业务员关联关系
     *
     * @param request 查询请求参数
     * @return 业务员关联客户列表
     */
    ResultModel<List<SalesmanRelationTravel>> queryByTravelId(SalesmanRelationTravelQueryByTravelIdRequest request);
} 