package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwClientMenuDO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.PageableDAO;

import java.util.List;

/**
 * 客户端菜单数据访问接口
 * 
 * <AUTHOR> 2025-07-24
 */
public interface PwClientMenuDao
        extends GenericDAO<Long, PwClientMenuDO, PwClientMenuUpdateArg, PwClientMenuDeleteArg>,
        CountableDAO<PwClientMenuQueryArg>,
        PageableDAO<PwClientMenuQueryArg, PwClientMenuDO> {

    /**
     * 根据父ID列表查询菜单列表
     *
     * @param parentIds 父ID列表
     * @return 菜单列表
     */
    List<PwClientMenuDO> queryByParentIds(List<Long> parentIds);

    /**
     * 查询已征用的菜单列表
     *
     * @param corpCode 公司代码
     * @return 已征用的菜单列表
     */
    List<PwClientMenuDO> queryHasRequisitionList(String corpCode);

    /**
     * 批量更新菜单为已征用状态
     *
     * @param arg 批量更新参数
     * @return 更新的记录数
     */
    int batchUpdateAsRequisition(PwClientMenuBatchUpdateAsRequisitionArg arg);
}
