package com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysFunctionsDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysFunctionsDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.*;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionCountableDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionPageableDAO;

import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统功能模块DAO实现
 * 
 * <AUTHOR> 2025-07-24
 */
@Getter
@Repository("pwSysFunctionsDao")
public class PwSysFunctionsDaoImpl implements PwSysFunctionsDao,
        SqlSessionGenericDAO<Long, PwSysFunctionsDO, PwSysFunctionsUpdateArg, PwSysFunctionsDeleteArg>,
        SqlSessionCountableDAO<PwSysFunctionsQueryArg>,
        SqlSessionPageableDAO<PwSysFunctionsQueryArg, PwSysFunctionsDO>,
        SqlSessionBatchInsertDAO<Long, PwSysFunctionsDO> {

    private final SqlSession sqlSession;
    private final Statement statement;

    public PwSysFunctionsDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(PwSysFunctionsDao.class);
    }

    @Override
    public List<PwSysFunctionsDO> queryBySubSystemId(Integer subsystemId) {
        Map<String, Object> params = new HashMap<>();
        params.put("subsystemId", subsystemId);
        return sqlSession.selectList(statement.get("queryBySubSystemId"), params);
    }

    @Override
    public List<PwSysFunctionsDO> queryByParentIds(List<Long> parentIds) {
        if (parentIds == null || parentIds.isEmpty()) {
            return new ArrayList<>();
        }
        Map<String, Object> params = new HashMap<>();
        params.put("parentIds", parentIds);
        return sqlSession.selectList(statement.get("queryByParentIds"), params);
    }

    @Override
    public List<PwSysFunctionsDO> queryHasRequisitionList(String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryHasRequisitionList"), params);
    }

    @Override
    public int batchUpdateAsRequisition(PwSysFunctionsBatchUpdateAsRequisitionArg arg) {
        if (arg.getIds() == null || arg.getIds().isEmpty()) {
            return 0;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("ids", arg.getIds());
        params.put("modifyBy", arg.getModifyBy());
        return sqlSession.update(statement.get("batchUpdateAsRequisition"), params);
    }
}
