package com.sendinfo.ticketing.verification.basic.component.account.converter;

import com.sendinfo.ticketing.verification.basic.api.account.request.CapitalAccountCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.account.request.CapitalAccountQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.account.mapper.CapitalAccountMapper;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountCreateParam;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountQueryParam;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.basic.repository.account.arg.CapitalAccountQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.account.arg.CapitalAccountUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.account.dataobject.CapitalAccountDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

/**
 * 资金账户转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("capitalAccountConverter")
public class CapitalAccountConverter implements ReadDo2ModelConverter<CapitalAccountDO, CapitalAccount>,
        CreateParam2DoConverter<CapitalAccountCreateParam, CapitalAccountDO>,
        CreateReq2ParamConverter<CapitalAccountCreateRequest, CapitalAccountCreateParam>,
        ReadParam2ArgConverter<CapitalAccountQueryParam, CapitalAccountQueryArg>,
        ReadReq2ParamConverter<CapitalAccountQueryRequest, CapitalAccountQueryParam>,
        UpdateParam2ArgConverter<CapitalAccountUpdateParam, CapitalAccountUpdateArg, CapitalAccount> {
    @Override
    public CapitalAccount r_d2m(CapitalAccountDO dataObject) {
        return CapitalAccountMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public CapitalAccountQueryArg r_p2a(CapitalAccountQueryParam param) {
        return CapitalAccountMapper.INSTANCE.convert(param);
    }

    /**
     * 创建参数转换为数据对象
     *
     * @param param 创建参数
     * @return 数据对象
     */
    @Override
    public CapitalAccountDO c_p2d(CapitalAccountCreateParam param) {
        return CapitalAccountMapper.INSTANCE.convert(param);
    }

    @Override
    public CapitalAccountQueryParam r_r2p(CapitalAccountQueryRequest queryRequest) {
        return CapitalAccountMapper.INSTANCE.convert(queryRequest);
    }

    @Override
    public CapitalAccountUpdateArg u_p2a(CapitalAccountUpdateParam updateParam, CapitalAccount currentModel) {
        return CapitalAccountMapper.INSTANCE.convert(updateParam);
    }

    @Override
    public CapitalAccountCreateParam c_r2p(CapitalAccountCreateRequest createRequest) {
        return CapitalAccountMapper.INSTANCE.convert(createRequest);
    }
}