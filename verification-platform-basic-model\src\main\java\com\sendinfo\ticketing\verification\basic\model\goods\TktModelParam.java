package com.sendinfo.ticketing.verification.basic.model.goods;

import com.sendinfo.ticketing.verification.basic.model.goods.properties.TktModelParamPropertyKey;
import com.sendinfo.ticketing.verification.common.model.properties.AbstractProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:14
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class TktModelParam extends AbstractProperties<TktModelParamPropertyKey> {
    private static final long serialVersionUID = 3884475207921745495L;
    /**
     * 主键ID
     */
    private Long id;
    private String title;
    private String modelType;
    private Integer modelKind;
    private String backImg;
    private String pageContent;
    private String hnPrintContent;
    private String printContent;
    private Integer width;
    private Integer height;
    private Integer printDirection;
    private String pageInfo;
    private String useFlag;
    private Integer roleApply;
    private String modelCode;
    private String continuousPrint;
    private String transferParam;

    /**
     * create by
     */
    private String createBy;

    /**
     * modified by
     */
    private String modifyBy;

    /**
     * 企业编码
     */
    private String corpCode;
}
