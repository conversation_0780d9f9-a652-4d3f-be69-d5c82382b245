package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktClassInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.TktClassInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 18:07
 */
public interface TktClassInfoReadService {

    /**
     * 查询票型种类列表
     *
     * @param request 条件
     * @return 票型种类列表
     */
    ResultModel<List<TktClassInfo>> queryClassInfoList(TktClassInfoQueryRequest request);

    ResultModel<List<TktClassInfo>> batchQueryClassInfoByClassCodes(@NotEmpty List<String> classCodes, @NotNull String corpCode);
}
