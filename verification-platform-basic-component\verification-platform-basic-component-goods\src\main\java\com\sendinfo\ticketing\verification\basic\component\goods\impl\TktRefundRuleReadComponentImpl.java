package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktRefundRuleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktRefundRuleConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktRefundRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktRefundRule;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktRefundRuleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktRefundRuleDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktRefundRuleDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedPageRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component("tktRefundRuleComponent")
@Getter
public class TktRefundRuleReadComponentImpl implements TktRefundRuleReadComponent,
        DaoBasedPageRead<Long, TktRefundRuleQueryParam, TktRefundRule, TktRefundRuleDO, TktRefundRuleQueryArg>,
        DaoBasedSingleRead<Long, TktRefundRuleQueryParam, TktRefundRule, TktRefundRuleDO>,
        DaoBasedCountRead<Long, TktRefundRuleQueryParam, TktRefundRule, TktRefundRuleQueryArg> {

    private final TktRefundRuleDao dao;
    private final TktRefundRuleConverter converter;

    public TktRefundRuleReadComponentImpl(TktRefundRuleDao dao, TktRefundRuleConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public TktRefundRule getTktRefundRuleByTicketId(String corpCode, Long ticketId) {
        TktRefundRuleDO tktRefundRuleDO = dao.getTktRefundRuleByTicketId(corpCode, ticketId);
        return Optional.ofNullable(tktRefundRuleDO)
                .map(converter::r_d2m)
                .orElse(null);
    }

    @Override
    public List<TktRefundRule> getTktRefundRuleByTicketIds(Set<Long> ticketIdSet, String corpCode) {
        return converter.r_ds2ms(dao.getTktRefundRuleByTicketIds(ticketIdSet, corpCode));
    }
}