package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.DictBatchQueryByCodesRequest;
import com.sendinfo.ticketing.verification.basic.api.system.request.DictQueryByCodeRequest;
import com.sendinfo.ticketing.verification.basic.model.system.Dict;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/23 16:08
 */
public interface DictReadService {

    /**
     * 查询字典
     *
     * @param request 条件
     * @return 字典
     */
    ResultModel<Dict> queryDictByDictCode(DictQueryByCodeRequest request);

    /**
     * 批量查询字典
     *
     * @param request 请求
     * @return 字典列表
     */
    ResultModel<List<Dict>> batchQueryDictByDictCodes(DictBatchQueryByCodesRequest request);
}
