package com.sendinfo.ticketing.verification.basic.component.customer.mapper;

import com.sendinfo.ticketing.verification.basic.component.customer.param.GuideQueryParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.Guide;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.AuditStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.GroupCoSyncEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.LangTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.TerminalTypeEnum;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.GuideQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.GuideDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 导游对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
    imports = {LangTypeEnum.class, AuditStatusEnum.class, CommonUseFlagEnum.class, TerminalTypeEnum.class, GroupCoSyncEnum.class}
)
public interface GuideMapper {

    GuideMapper INSTANCE = Mappers.getMapper(GuideMapper.class);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    @Mapping(target = "langType", expression = "java(queryParam.getLangType() != null ? queryParam.getLangType().getCode() : null)")
    @Mapping(target = "auditStatus", expression = "java(queryParam.getAuditStatus() != null ? queryParam.getAuditStatus().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(queryParam.getUseFlag() != null ? queryParam.getUseFlag().getCode() : null)")
    @Mapping(target = "terminalType", expression = "java(queryParam.getTerminalType() != null ? queryParam.getTerminalType().getCode() : null)")
    @Mapping(target = "isGroupCoSync", expression = "java(queryParam.getIsGroupCoSync() != null ? queryParam.getIsGroupCoSync().getCode() : null)")
    GuideQueryArg convert(GuideQueryParam queryParam);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    @Mapping(target = "langType", expression = "java(LangTypeEnum.ofCode(dataObject.getLangType()))")
    @Mapping(target = "auditStatus", expression = "java(AuditStatusEnum.ofCode(dataObject.getAuditStatus()))")
    @Mapping(target = "useFlag", expression = "java(CommonUseFlagEnum.ofCode(dataObject.getUseFlag()))")
    @Mapping(target = "terminalType", expression = "java(TerminalTypeEnum.ofCode(dataObject.getTerminalType()))")
    @Mapping(target = "isGroupCoSync", expression = "java(GroupCoSyncEnum.ofCode(dataObject.getIsGroupCoSync()))")
    Guide convert(GuideDO dataObject);
} 