package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.customer.request.SalesmanRelationTravelQueryByTravelIdRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.SalesmanRelationTravelReadService;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanRelationTravel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/14 10:46
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class SalesmanRelationTravelReadServiceImplTest {

    @Autowired
    private SalesmanRelationTravelReadService salesmanRelationTravelReadService;

//    @Test
    public void testQueryByTravelIdSuccess() {

        SalesmanRelationTravelQueryByTravelIdRequest request = new SalesmanRelationTravelQueryByTravelIdRequest();
        request.setCorpCode("0001/10241597/");
        request.setTravelId(71733L);

        ResultModel<List<SalesmanRelationTravel>> resultModel = salesmanRelationTravelReadService.queryByTravelId(request);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertFalse(resultModel.getModel().isEmpty());
    }

}
