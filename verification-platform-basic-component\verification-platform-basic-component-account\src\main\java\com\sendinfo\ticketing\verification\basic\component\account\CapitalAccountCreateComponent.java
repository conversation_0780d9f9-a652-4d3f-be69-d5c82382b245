package com.sendinfo.ticketing.verification.basic.component.account;

import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountCreateParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.common.component.CreateComponent;

/**
 * 资金账户创建组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CapitalAccountCreateComponent extends CreateComponent<CapitalAccountCreateParam, Long> {

	/**
	 * 创建资金账户
	 *
	 * @param createParam   创建参数
	 * @return  CapitalAccount
	 */
	CapitalAccount createCapitalAccount(CapitalAccountCreateParam createParam);
} 