package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.model.pay.enums.BusTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 收入主体业务设置更新参数
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktMainbodyBusUpdateParam extends AbstractUpdateParam<Long> {
    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 主体名称
     */
    private String mainbodyName;

    /**
     * 业务类型
     */
    private BusTypeEnum busType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 组标识
     */
    private String groupFlag;

    /**
     * 组标识状态更新器
     */
    private StatusUpdater<String> groupFlagUpdater;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;
} 