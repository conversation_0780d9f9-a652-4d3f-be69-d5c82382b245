package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.MainbodyChanleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.MainbodyChanleConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.MainbodyChanle;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.MainbodyChanleDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 商户渠道配置读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("mainbodyChanleReadComponent")
@Getter
public class MainbodyChanleReadComponentImpl implements
        MainbodyChanleReadComponent,
        DaoBasedSingleRead<Long, MainbodyChanleQueryParam, MainbodyChanle, MainbodyChanleDO>,
        DaoBasedListRead<Long, MainbodyChanleQueryParam, MainbodyChanle, MainbodyChanleDO, MainbodyChanleQueryArg>,
        DaoBasedCountRead<Long, MainbodyChanleQueryParam, MainbodyChanle, MainbodyChanleQueryArg> {

    private final MainbodyChanleDao dao;
    private final MainbodyChanleConverter converter;

    public MainbodyChanleReadComponentImpl(MainbodyChanleDao dao, MainbodyChanleConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 