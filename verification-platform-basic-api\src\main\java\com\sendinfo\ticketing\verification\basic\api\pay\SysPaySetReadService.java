package com.sendinfo.ticketing.verification.basic.api.pay;

import com.sendinfo.ticketing.verification.basic.api.pay.request.SysPaySetEnableQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/25 11:28
 */
public interface SysPaySetReadService {


    /**
     * 根据条件查询启用的支付方式
     *
     * @param request 查询条件
     * @return 支付方式
     */
    ResultModel<List<SysPaySet>> queryEnableListByPayTypeAndId(SysPaySetEnableQueryRequest request);
}
