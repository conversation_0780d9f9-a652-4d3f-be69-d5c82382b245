package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体子商户创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageSubMerchantsCreateParam extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 子商户名称
     */
    private String subMerchantsName;

    /**
     * 子商户号
     */
    private String merchantNo;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String modifyBy;
} 