package com.sendinfo.ticketing.verification.basic.api.auth;

import com.sendinfo.ticketing.verification.basic.model.auth.UserSessionData;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * <AUTHOR>
 * @since 2025-07-23 15:03:47
 */
public interface UserSessionDataReadService {

    /**
     * 获取老票务token
     *
     * @param request 请求
     * @return session data
     */
    ResultModel<UserSessionData> getUserSessionDataFromOldPwJwtToken(UserSessionDataGetRequest request);
}
