package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.UserRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.UserRole;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色用户表读取服务接口
 * 对应表：user_role
 * 方法、注释、参数、返回值类型严格对齐 system/api 层
 * <AUTHOR>
 */
public interface UserRoleReadService {

    /**
     * 根据主键ID查询角色用户信息
     *
     * @param id 主键ID
     * @return 角色用户信息
     */
    ResultModel<UserRole> queryById(@NotNull Long id);

    /**
     * 根据条件查询角色用户列表
     *
     * @param request 查询条件
     * @return 角色用户列表
     */
    ResultModel<List<UserRole>> queryList(UserRoleQueryRequest request);

    /**
     * 根据条件查询角色用户列表
     *
     * @param userId 用户ID
     * @return 角色用户列表
     */
    ResultModel<List<UserRole>> queryListByUserId(Long userId);
} 