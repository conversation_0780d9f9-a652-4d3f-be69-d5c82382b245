/**
 *  客户端菜单查询功能类
 *
 *  <AUTHOR> 2025-07-25 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.service.infra.function;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwClientMenuQueryCondition;
import com.sendinfo.ticketing.verification.basic.component.infra.PwClientMenuReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwClientMenuConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwClientMenuQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.PwClientMenuAttachmentKey;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import com.sendinfo.ticketing.verification.common.service.function.QueryPageByConditionFunction;
import com.sendinfo.ticketing.verification.common.service.stereotype.Function;
import com.sendinfo.ticketing.verification.common.service.support.QueryPageByConditionLogicAction;
import com.sendinfo.ticketing.verification.flow.Hint;
import com.sendinfo.ticketing.verification.flow.Question;
import com.sendinfo.ticketing.verification.flow.function.LogicAction;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sendinfo.ticketing.verification.basic.service.infra.enums.PwClientMenuAttachmentKey.PW_CLIENT_MENU_DATA_COUNT_ATTACHMENT_KEY;
import static com.sendinfo.ticketing.verification.basic.service.infra.enums.PwClientMenuAttachmentKey.PW_CLIENT_MENU_DATA_LIST_ATTACHMENT_KEY;

/**
 * 客户端菜单查询功能类
 *
 * <AUTHOR> 2025-07-25 15:30:00
 */
@Getter
@Function("queryPwClientMenuFunction")
@Slf4j
public class QueryPwClientMenuFunction implements QueryPageByConditionFunction<PwClientMenuQueryCondition> {

    private final LogicAction<PageRequest<PwClientMenuQueryCondition>> queryPageByCondition;
    private final PwClientMenuReadComponent pwClientMenuReadComponent;
    private final PwClientMenuConverter converter;

    public QueryPwClientMenuFunction(PwClientMenuReadComponent pwClientMenuReadComponent,
            PwClientMenuConverter converter) {
        this.pwClientMenuReadComponent = pwClientMenuReadComponent;
        this.converter = converter;

        this.queryPageByCondition = new QueryPageByConditionLogicAction<>(
                pwClientMenuReadComponent,
                this.converter::r_pr2p,
                PW_CLIENT_MENU_DATA_LIST_ATTACHMENT_KEY,
                PW_CLIENT_MENU_DATA_COUNT_ATTACHMENT_KEY);
    }

    /**
     * 查询树形结构的菜单分页列表，分页依据根节点，分页列表元素是根节点，用树形结构显示根节点下的所有子节点
     */
    public Hint queryTreePageList(Question<PageRequest<PwClientMenuQueryCondition>> question) {
        PageRequest<PwClientMenuQueryCondition> pageRequest = question.getBody();
        PwClientMenuQueryParam pwClientMenuQueryParam = this.converter.r_pr2p(pageRequest);
        int count = pwClientMenuReadComponent.count(pwClientMenuQueryParam);
        List<PwClientMenu> rootMenus = Collections.emptyList();
        if (count > 0) {
            // 只会查询到一级节点
            rootMenus = pwClientMenuReadComponent.list(pwClientMenuQueryParam);
            buildMenuTree(rootMenus);
        }

        question.setAttachment(PW_CLIENT_MENU_DATA_LIST_ATTACHMENT_KEY, rootMenus);
        question.setAttachment(PW_CLIENT_MENU_DATA_COUNT_ATTACHMENT_KEY, count);
        return Hint.gotoNext();
    }

    /**
     * 查询树形结构的已征用菜单列表
     */
    public Hint queryHasRequisitionTreeList(Question<String> question) {
        // 获取公司代码
        String corpCode = question.getBody();

        // 调用组件方法获取所有已征用的节点
        List<PwClientMenu> allRequisitionMenus = pwClientMenuReadComponent.queryHasRequisitionList(corpCode);

        // 组装树结构
        List<PwClientMenu> treeMenus = buildMenuTreeFromList(allRequisitionMenus);

        question.setAttachment(PwClientMenuAttachmentKey.PW_CLIENT_MENU_TREE_LIST_ATTACHMENT_KEY, treeMenus);
        return Hint.gotoNext();
    }

    /**
     * 构建菜单树结构（从根节点开始递归查询）
     */
    private List<PwClientMenu> buildMenuTree(List<PwClientMenu> rootMenus) {
        if (rootMenus == null || rootMenus.isEmpty()) {
            return new ArrayList<>();
        }
        for (PwClientMenu menu : rootMenus) {
            loadChildren(menu, 0);
        }

        return rootMenus;
    }

    /**
     * 递归加载子节点
     */
    private void loadChildren(PwClientMenu parentMenu, int level) {
        // 最多递归5级
        if (level >= 4) {
            return;
        }
        List<Long> parentIds = List.of(parentMenu.getId());
        List<PwClientMenu> children = pwClientMenuReadComponent.queryByParentIds(parentIds);

        if (children != null && !children.isEmpty()) {
            parentMenu.setChildren(children);
            // 递归加载每个子节点的子节点
            for (PwClientMenu child : children) {
                loadChildren(child, level + 1);
            }
        }
    }

    /**
     * 从扁平列表构建树结构
     */
    private List<PwClientMenu> buildMenuTreeFromList(List<PwClientMenu> allMenus) {
        if (allMenus == null || allMenus.isEmpty()) {
            return new ArrayList<>();
        }

        // 按ID建立索引
        Map<Long, PwClientMenu> menuMap = allMenus.stream()
                .collect(Collectors.toMap(PwClientMenu::getId, menu -> menu, (existing, replacement) -> existing));

        List<PwClientMenu> rootMenus = new ArrayList<>();

        for (PwClientMenu menu : allMenus) {
            Integer parentId = menu.getParentId();
            if (parentId == null || parentId == 0) {
                // 根节点
                rootMenus.add(menu);
            } else {
                // 子节点，找到父节点并添加到其children中
                PwClientMenu parent = menuMap.get(parentId.longValue());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(menu);
                }
            }
        }

        return rootMenus;
    }
}
