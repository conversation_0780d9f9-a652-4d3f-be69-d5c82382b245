package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.PwClientMenuUpdateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwClientMenuConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwClientMenuUpdateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwClientMenuBatchUpdateAsRequisitionParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwClientMenuDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 * 客户端菜单更新组件实现
 *
 * <AUTHOR> Generated 2025-07-25
 */
@Component("pwClientMenuUpdateComponent")
@Getter
public class PwClientMenuUpdateComponentImpl implements PwClientMenuUpdateComponent,
        DaoBasedSingleUpdate<PwClientMenuUpdateParam, PwClientMenuUpdateArg, PwClientMenu> {

    private final PwClientMenuDao dao;
    private final PwClientMenuConverter converter;

    public PwClientMenuUpdateComponentImpl(PwClientMenuDao dao, PwClientMenuConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public int batchUpdateAsRequisition(PwClientMenuBatchUpdateAsRequisitionParam param) {
        PwClientMenuBatchUpdateAsRequisitionArg arg = new PwClientMenuBatchUpdateAsRequisitionArg();
        arg.setCorpCode(param.getCorpCode());
        arg.setIds(param.getIds());
        arg.setModifyBy(param.getModifyBy());

        return dao.batchUpdateAsRequisition(arg);
    }
}
