/**
 * 子系统管理查询组件实现类单元测试
 *
 * <AUTHOR> 2025-07-27 15:40:00
 */
package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwSysSubsystemConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysSubsystemQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysSubsystem;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysSubsystemDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysSubsystemDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PwSysSubsystemReadComponentImplTest {

    @Mock
    private PwSysSubsystemDao dao;

    private PwSysSubsystemConverter converter;
    private PwSysSubsystemReadComponentImpl component;

    @Before
    public void setUp() {
        converter = new PwSysSubsystemConverter();
        component = new PwSysSubsystemReadComponentImpl(dao, converter);
    }



    @Test
    public void testQueryAllInUseList() {
        String corpCode = "testCorpCode";
        // Given
        List<PwSysSubsystemDO> dataObjects = Arrays.asList(createTestDO(), createTestDO());

        when(dao.queryAllInUseList(anyString())).thenReturn(dataObjects);

        // When
        List<PwSysSubsystem> result = component.queryAllInUseList(corpCode);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(dao, times(1)).queryAllInUseList(corpCode);
        
        // Verify the conversion is correct
        PwSysSubsystem firstResult = result.get(0);
        assertEquals("TEST_CORP", firstResult.getCorpCode());
        assertEquals("Test Module", firstResult.getApplyModel());
        assertEquals("Test Application", firstResult.getApplyName());
        assertEquals("T", firstResult.getUseFlag());
    }

    @Test
    public void testQueryAllInUseListWithEmptyResult() {
        String corpCode = "testCorpCode";
        // Given
        when(dao.queryAllInUseList(corpCode)).thenReturn(Collections.emptyList());

        // When
        List<PwSysSubsystem> result = component.queryAllInUseList(corpCode);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dao, times(1)).queryAllInUseList(corpCode);
    }

    @Test
    public void testQueryAllInUseListWithNullResult() {
        String corpCode = "testCorpCode";
        // Given
        when(dao.queryAllInUseList(corpCode)).thenReturn(null);

        // When
        List<PwSysSubsystem> result = component.queryAllInUseList(corpCode);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dao, times(1)).queryAllInUseList(corpCode);
    }

    private PwSysSubsystemQueryParam createTestQueryParam() {
        PwSysSubsystemQueryParam param = new PwSysSubsystemQueryParam();
        param.setCorpCode("TEST_CORP");
        param.setApplyModel("Test Module");
        param.setApplyName("Test Application");
        param.setUseFlag("T");
        param.setApplyCode("TEST_CODE");
        param.setStartIndex(0);
        param.setPageSize(10);
        return param;
    }

    private PwSysSubsystemDO createTestDO() {
        PwSysSubsystemDO dataObject = new PwSysSubsystemDO();
        dataObject.setId(1L);
        dataObject.setCorpCode("TEST_CORP");
        dataObject.setApplyModel("Test Module");
        dataObject.setApplyName("Test Application");
        dataObject.setApplyDesc("Test Description");
        dataObject.setUseFlag("T");
        dataObject.setSortNo(1);
        dataObject.setApplyCode("TEST_CODE");
        dataObject.setCreateBy("testUser");
        dataObject.setCreateTime(new Date());
        dataObject.setModifyBy("testUser");
        dataObject.setModifyTime(new Date());
        dataObject.setDeleted("F");
        return dataObject;
    }
}