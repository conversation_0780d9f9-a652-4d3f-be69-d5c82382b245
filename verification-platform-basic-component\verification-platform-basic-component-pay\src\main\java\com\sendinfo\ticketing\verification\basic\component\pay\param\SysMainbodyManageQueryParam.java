package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置查询参数
 * 用于封装查询经营主体支付配置所需的参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageQueryParam extends AbstractPageQueryParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 经营主体ID
     */
    private Long mainbodyId;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 经营主体名称（模糊查询）
     */
    private String mainbodyName;

    /**
     * 经营主体简称（模糊查询）
     */
    private String mainbodyShortName;

    /**
     * 支付渠道名称（模糊查询）
     */
    private String payChannelName;

    /**
     * 支付渠道编号（精确查询）
     */
    private String payChannelCode;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 联系人（模糊查询）
     */
    private String linkMan;

    /**
     * 联系电话（精确查询）
     */
    private String linkTel;

    /**
     * 经营主体编号（精确查询）
     */
    private String mainbodyNumber;
} 