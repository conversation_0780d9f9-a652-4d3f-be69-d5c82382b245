package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 经营主体支付配置对象映射器
 * 使用MapStruct自动生成对象转换实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SysMainbodyManageMapper {

    SysMainbodyManageMapper INSTANCE = Mappers.getMapper(SysMainbodyManageMapper.class);

    /**
     * 创建参数转换为数据对象
     */
    @Mapping(target = "useFlag", source = "useFlag", qualifiedByName = "enumToString")
    SysMainbodyManageDO convert(SysMainbodyManageCreateParam createParam);

    /**
     * 查询参数转换为查询参数
     */
    @Mapping(target = "useFlag", source = "useFlag", qualifiedByName = "enumToString")
    SysMainbodyManageQueryArg convert(SysMainbodyManageQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     */
    @Mapping(target = "useFlag", source = "useFlag", qualifiedByName = "enumToString")
    SysMainbodyManageUpdateArg convert(SysMainbodyManageUpdateParam updateParam);

    /**
     * 数据对象转换为模型对象
     */
    @Mapping(target = "useFlag", source = "useFlag", qualifiedByName = "stringToEnum")
    SysMainbodyManage convert(SysMainbodyManageDO dataObject);

    /**
     * 枚举转字符串
     */
    @Named("enumToString")
    default String enumToString(CommonUseFlagEnum useFlag) {
        return useFlag != null ? useFlag.getCode() : null;
    }

    /**
     * 字符串转枚举
     */
    @Named("stringToEnum")
    default CommonUseFlagEnum stringToEnum(String useFlag) {
        return useFlag != null ? CommonUseFlagEnum.ofCode(useFlag) : null;
    }
} 