package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageMmSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 经营主体支付配置子商户关联表映射接口
 * 使用MapStruct进行对象映射转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysMainbodyManageMmSubMerchantsMapper {

    SysMainbodyManageMmSubMerchantsMapper INSTANCE = Mappers.getMapper(SysMainbodyManageMmSubMerchantsMapper.class);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    SysMainbodyManageMmSubMerchantsDO convert(SysMainbodyManageMmSubMerchantsCreateParam createParam);

    /**
     * 数据对象转换为模型
     *
     * @param dataObject 数据对象
     * @return 模型
     */
    SysMainbodyManageMmSubMerchants convert(SysMainbodyManageMmSubMerchantsDO dataObject);

    /**
     * 数据对象列表转换为模型列表
     *
     * @param dataObjects 数据对象列表
     * @return 模型列表
     */
    List<SysMainbodyManageMmSubMerchants> convert(List<SysMainbodyManageMmSubMerchantsDO> dataObjects);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    SysMainbodyManageMmSubMerchantsQueryArg convert(SysMainbodyManageMmSubMerchantsQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     *
     * @param updateParam 更新参数
     * @return 更新参数
     */
    SysMainbodyManageMmSubMerchantsUpdateArg convert(SysMainbodyManageMmSubMerchantsUpdateParam updateParam);
} 