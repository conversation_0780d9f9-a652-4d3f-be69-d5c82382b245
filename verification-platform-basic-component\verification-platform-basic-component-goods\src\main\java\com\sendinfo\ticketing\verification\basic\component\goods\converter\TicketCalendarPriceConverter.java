package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TicketCalendarPriceMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TicketCalendarPriceQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TicketCalendarPrice;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/7/26 18:07
 **/
@Component("ticketCalendarPriceConverter")
public class TicketCalendarPriceConverter implements
        ReadParam2ArgConverter<TicketCalendarPriceQueryParam, TicketCalendarPriceQueryArg>,
        ReadDo2ModelConverter<TicketCalendarPriceDO, TicketCalendarPrice> {
    @Override
    public TicketCalendarPriceQueryArg r_p2a(TicketCalendarPriceQueryParam param) {
        return TicketCalendarPriceMapper.INSTANCE.convert(param);
    }

    @Override
    public TicketCalendarPrice r_d2m(TicketCalendarPriceDO dataObject) {
        return TicketCalendarPriceMapper.INSTANCE.convert(dataObject);
    }
}
