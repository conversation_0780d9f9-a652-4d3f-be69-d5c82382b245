package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.PwClientMenuQueryCondition;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 客户端菜单查询服务
 * 
 * <AUTHOR> 2025-07-24
 */
public interface PwClientMenuReadService {

    /**
     * 分页查询客户端菜单
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<PwClientMenu> queryTreePageList(PageRequest<PwClientMenuQueryCondition> pageRequest);

    /**
     * 查询已征用的菜单列表
     *
     * @param corpCode 公司代码
     * @return 已征用的菜单列表
     */
    ResultModel<List<PwClientMenu>> queryHasRequisitionList(String corpCode);
}
