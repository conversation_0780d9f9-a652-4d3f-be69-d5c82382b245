package com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl;

import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwClientMenuDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwClientMenuDO;
import com.sendinfo.ticketing.verification.repository.jdbc.Statement;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionCountableDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionGenericDAO;
import com.sendinfo.ticketing.verification.repository.jdbc.mybatis.SqlSessionPageableDAO;

import lombok.Getter;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户端菜单DAO实现
 * 
 * <AUTHOR> 2025-07-24
 */
@Getter
@Repository("pwClientMenuDao")
public class PwClientMenuDaoImpl implements PwClientMenuDao,
        SqlSessionGenericDAO<Long, PwClientMenuDO, PwClientMenuUpdateArg, PwClientMenuDeleteArg>,
        SqlSessionCountableDAO<PwClientMenuQueryArg>,
        SqlSessionPageableDAO<PwClientMenuQueryArg, PwClientMenuDO>,
        SqlSessionDAO {

    private final SqlSession sqlSession;
    private final Statement statement;

    public PwClientMenuDaoImpl(@Qualifier("saasPwSqlSessionTemplate") SqlSessionTemplate sqlSessionTemplate) {
        this.sqlSession = sqlSessionTemplate;
        this.statement = Statement.of(PwClientMenuDao.class);
    }

    @Override
    public List<PwClientMenuDO> queryByParentIds(List<Long> parentIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("parentIds", parentIds);
        return sqlSession.selectList(statement.get("queryByParentIds"), params);
    }

    @Override
    public List<PwClientMenuDO> queryHasRequisitionList(String corpCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("corpCode", corpCode);
        return sqlSession.selectList(statement.get("queryHasRequisitionList"), params);
    }

    @Override
    public int batchUpdateAsRequisition(PwClientMenuBatchUpdateAsRequisitionArg arg) {
        if (arg.getIds() == null || arg.getIds().isEmpty()) {
            return 0;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("ids", arg.getIds());
        params.put("modifyBy", arg.getModifyBy());
        return sqlSession.update(statement.get("batchUpdateAsRequisition"), params);
    }
}
