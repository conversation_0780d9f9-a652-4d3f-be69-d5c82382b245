package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.model.pay.enums.BusTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 收入主体业务设置查询参数
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktMainbodyBusQueryParam extends AbstractQueryParam {
    /**
     * 业务ID
     */
    private Long busId;

    /**
     * 业务类型
     */
    private BusTypeEnum busType;

    /**
     * 组标识
     */
    private String groupFlag;

    /**
     * 企业码
     */
    private String corpCode;
} 