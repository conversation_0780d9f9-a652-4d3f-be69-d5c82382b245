package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TicketCalendarPriceQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TicketCalendarPrice;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/26 17:30
 **/
public interface TicketCalendarPriceReadComponent extends ReadComponent<Long, TicketCalendarPriceQueryParam, TicketCalendarPrice>  {
    /**
     * 批量获取票据日历价格
     * @param ticketIds
     * @param corpCode
     * @return
     */
    List<TicketCalendarPrice> batchQueryByTicketIds(List<Long> ticketIds, String corpCode);
}
