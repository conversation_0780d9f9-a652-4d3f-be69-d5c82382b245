package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.SysMainbodyManageMmSubMerchantsReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.SysMainbodyManageMmSubMerchantsConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageMmSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageMmSubMerchantsDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 经营主体支付配置子商户关联表读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyManageMmSubMerchantsReadComponent")
@Getter
public class SysMainbodyManageMmSubMerchantsReadComponentImpl implements
        SysMainbodyManageMmSubMerchantsReadComponent,
        DaoBasedSingleRead<Long, SysMainbodyManageMmSubMerchantsQueryParam, SysMainbodyManageMmSubMerchants, SysMainbodyManageMmSubMerchantsDO>,
        DaoBasedListRead<Long, SysMainbodyManageMmSubMerchantsQueryParam, SysMainbodyManageMmSubMerchants, SysMainbodyManageMmSubMerchantsDO, SysMainbodyManageMmSubMerchantsQueryArg>,
        DaoBasedCountRead<Long, SysMainbodyManageMmSubMerchantsQueryParam, SysMainbodyManageMmSubMerchants, SysMainbodyManageMmSubMerchantsQueryArg> {

    private final SysMainbodyManageMmSubMerchantsDao dao;
    private final SysMainbodyManageMmSubMerchantsConverter converter;

    public SysMainbodyManageMmSubMerchantsReadComponentImpl(SysMainbodyManageMmSubMerchantsDao dao, SysMainbodyManageMmSubMerchantsConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 