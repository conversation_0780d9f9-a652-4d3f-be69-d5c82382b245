package com.sendinfo.ticketing.verification.basic.model.goods.error;

import com.sendinfo.ticketing.verification.basic.model.goods.domain.GoodsDomainDefinition;
import com.sendinfo.ticketing.verification.common.model.domain.DomainDefinition;
import com.sendinfo.ticketing.verification.common.model.error.ErrorDefinition;
import com.sendinfo.ticketing.verification.common.model.service.ServiceDefinition;

import static com.sendinfo.ticketing.verification.common.model.enums.ProductCode.TICKET_PLATFORM;

public enum GoodsErrorDef implements ErrorDefinition {

    QUERY_TICKET_MODEL_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "001", "ticket-platform.goods.query-ticket-model-error"),
    TICKET_MODEL_NOT_FOUND_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "002", "ticket-platform.goods.ticket-model-not-found-error"),
    QUERY_SALE_RULE_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "003", "ticket-platform.goods.query-sale-rule-error"),
    SALE_RULE_NOT_FOUND_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "004", "ticket-platform.goods.sale-rule-not-found-error"),
    QUERY_TICKET_DIV_RULE_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "005", "ticket-platform.goods.query-ticket-div-rule-error"),
    TICKET_DIV_RULE_NOT_FOUND_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "006", "ticket-platform.goods.ticket-div-rule-not-found-error"),
    QUERY_CLASS_INFO_ERROR(TICKET_PLATFORM,  GoodsDomainDefinition.GOODS, "007", "ticket-platform.goods.query-class-info-error"),
    QUERY_GROUP_INFO_ERROR(TICKET_PLATFORM,  GoodsDomainDefinition.GOODS, "008", "ticket-platform.goods.query-group-info-error"),

    QUERY_TKT_PRINT_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "009", "ticket-platform.goods.query-tkt-print-error"),
    QUERY_TKT_PRINT_NOT_EXISTS(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "010", "ticket-platform.goods.query-tkt-print-not-exist"),

    QUERY_TKT_REFUND_RULE_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "011", "ticket-platform.goods.query-tkt-refund-rule-error"),
    QUERY_TKT_REFUND_RULE_NOT_EXISTS(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "012", "ticket-platform.goods.query-tkt-refund-rule-not-exist"),

    QUERY_TKT_TICKET_TYPE_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "013", "ticket-platform.goods.query-tkt-ticket-type-error"),

    QUERY_EXPLAINER_SERVICE_CATEGORY_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "014", "ticket-platform.goods.query-explainer-service-category-error"),
    QUERY_EXPLAINER_INFO_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "015", "ticket-platform.goods.query-explainer-info-error"),
    QUERY_EXPLAINER_PAY_INFO_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "016", "ticket-platform.goods.query-explainer-pay-info-error"),

    GOODS_PROPERTY_SAVE_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "017", "ticket-platform.goods.goods-property-save-error"),

    GOODS_SEARCH_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "018", "ticket-platform.goods.goods-search-error"),

    QUERY_TKT_CALENDAR_PRICE_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "019", "ticket-platform.goods.query-tkt-calendar-price-error"),

    QUERY_TKT_MODEL_PARAM_ERROR(TICKET_PLATFORM, GoodsDomainDefinition.GOODS, "020", "ticket-platform.goods.query-tkt-model-param-error"),
    ;


    private final String code;
    private final String resourceKey;

    GoodsErrorDef(ServiceDefinition serverDefinition, DomainDefinition domainDefinition, String functionCode,
                  String resourceBundlerResourceKey) {
        this.code = String.join("-", serverDefinition.code(), domainDefinition.code(), functionCode);
        this.resourceKey = resourceBundlerResourceKey;
    }

    @Override
    public String code() {
        return code;
    }

    @Override
    public String resourceKey() {
        return resourceKey;
    }
}
