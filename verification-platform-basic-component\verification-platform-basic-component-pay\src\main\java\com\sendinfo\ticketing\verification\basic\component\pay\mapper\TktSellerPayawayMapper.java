package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.ClientTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.SaleModelEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 售票员收款方式映射接口
 * 使用MapStruct进行对象映射转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
        imports = {CommonUseFlagEnum.class, SaleModelEnum.class, ClientTypeEnum.class}
)
public interface TktSellerPayawayMapper {

    TktSellerPayawayMapper INSTANCE = Mappers.getMapper(TktSellerPayawayMapper.class);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    @Mapping(target = "saleModel", expression = "java(createParam.getSaleModel() != null ? createParam.getSaleModel().getCode() : null)")
    @Mapping(target = "clientType", expression = "java(createParam.getClientType() != null ? createParam.getClientType().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(createParam.getUseFlag() != null ? createParam.getUseFlag().getCode() : null)")
    TktSellerPayawayDO convert(TktSellerPayawayCreateParam createParam);

    /**
     * 数据对象转换为模型
     *
     * @param dataObject 数据对象
     * @return 模型
     */
    @Mapping(target = "saleModel", expression = "java(SaleModelEnum.ofCode(dataObject.getSaleModel()))")
    @Mapping(target = "clientType", expression = "java(ClientTypeEnum.ofCode(dataObject.getClientType()))")
    @Mapping(target = "useFlag", expression = "java(CommonUseFlagEnum.ofCode(dataObject.getUseFlag()))")
    TktSellerPayaway convert(TktSellerPayawayDO dataObject);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    @Mapping(target = "saleModel", expression = "java(queryParam.getSaleModel() != null ? queryParam.getSaleModel().getCode() : null)")
    @Mapping(target = "clientType", expression = "java(queryParam.getClientType() != null ? queryParam.getClientType().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(queryParam.getUseFlag() != null ? queryParam.getUseFlag().getCode() : null)")
    TktSellerPayawayQueryArg convert(TktSellerPayawayQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     *
     * @param updateParam  更新参数
     * @param currentModel 当前模型
     * @return 更新参数
     */
    @Mapping(target = "saleModel", expression = "java(updateParam.getSaleModel() != null ? updateParam.getSaleModel().getCode() : null)")
    @Mapping(target = "clientType", expression = "java(updateParam.getClientType() != null ? updateParam.getClientType().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(updateParam.getUseFlag() != null ? updateParam.getUseFlag().getCode() : null)")
    TktSellerPayawayUpdateArg convert(TktSellerPayawayUpdateParam updateParam);
}