/**
 *  编码生成工具类
 *
 *  <AUTHOR> 2025-01-27 10:30:00
 */
package com.sendinfo.ticketing.verification.basic.common.util;

import java.util.concurrent.atomic.AtomicLong;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 编码生成工具类
 * 
 * <AUTHOR> 2025-01-27 10:30:00
 */
public class CodeGeneratorUtil {

    /**
     * 时间戳格式化器：yyyyMMddHHmmssSSS（17位）
     */
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");

    /**
     * 序列号生成器，保证线程安全和唯一性
     */
    private static final AtomicLong SEQUENCE = new AtomicLong(0);


    /**
     * 生成基于时间戳的编码（带前缀）
     * 格式：前缀 + 时间戳（yyyyMMddHHmmssSSS，17位）+ 序列号4位
     * 总长度：前缀 + 21位
     * 重复概率：0（绝对唯一）
     *
     * @param prefix 前缀
     * @return 生成的编码
     */
    public static String generateCode(String prefix) {
        StringBuilder codeBuilder = new StringBuilder();

        if (prefix == null || prefix.isEmpty()) {
            throw new IllegalArgumentException("Prefix cannot be null or empty.");
        }
        // 添加前缀
        codeBuilder.append(prefix);

        // 添加时间戳（yyyyMMddHHmmssSSS格式，17位）
        LocalDateTime now = LocalDateTime.now();
        String timestamp = now.format(TIMESTAMP_FORMATTER);
        codeBuilder.append(timestamp);

        // 添加4位序列号（使用AtomicLong保证线程安全和唯一性）
        // 序列号循环使用：0000-9999，绝对唯一
        long seq = SEQUENCE.getAndIncrement() % 10000;
        codeBuilder.append(String.format("%04d", seq));

        return codeBuilder.toString();
    }
}