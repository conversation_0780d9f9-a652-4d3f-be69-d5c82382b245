package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;

/**
 * 支付渠道查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class PayChanleQueryParam extends AbstractPageQueryParam {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 渠道名称：建行、农行
     */
    private String chanleName;

    /**
     * 渠道编码:对应支付中心的渠道产品编号
     */
    private String chanleCode;

    /**
     * 支付产品码：对应支付中心的支付产品码
     */
    private String payProductCode;

    /**
     * 启用标识
     */
    private CommonUseFlagEnum useFlag;
} 