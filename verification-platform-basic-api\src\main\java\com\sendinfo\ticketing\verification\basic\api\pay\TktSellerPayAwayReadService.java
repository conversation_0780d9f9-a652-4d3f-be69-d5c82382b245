package com.sendinfo.ticketing.verification.basic.api.pay;

import com.sendinfo.ticketing.verification.basic.api.pay.request.TktSellerPayAwayEnableQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/25 14:10
 */
public interface TktSellerPayAwayReadService {


    /**
     * 查询售票员启用的支付方式
     *
     * @param request 查询参数
     * @return 售票员启用的支付方式
     */
    ResultModel<List<TktSellerPayaway>> queryEnableSellerPayAwayList(TktSellerPayAwayEnableQueryRequest request);
}
