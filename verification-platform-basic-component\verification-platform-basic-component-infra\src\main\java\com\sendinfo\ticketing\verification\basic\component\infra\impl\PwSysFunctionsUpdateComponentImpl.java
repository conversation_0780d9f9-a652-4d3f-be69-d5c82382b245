package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import com.sendinfo.ticketing.verification.basic.component.infra.PwSysFunctionsUpdateComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwSysFunctionsConverter;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysFunctionsUpdateParam;
import com.sendinfo.ticketing.verification.basic.component.infra.param.PwSysFunctionsBatchUpdateAsRequisitionParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysFunctions;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysFunctionsDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;

/**
 * 系统功能模块更新组件实现
 * 
 * <AUTHOR> Generated 2025-07-24
 */
@Component("pwSysFunctionsUpdateComponent")
@Getter
public class PwSysFunctionsUpdateComponentImpl implements PwSysFunctionsUpdateComponent,
        DaoBasedSingleUpdate<PwSysFunctionsUpdateParam, PwSysFunctionsUpdateArg, PwSysFunctions> {

    private final PwSysFunctionsDao dao;
    private final PwSysFunctionsConverter converter;

    public PwSysFunctionsUpdateComponentImpl(PwSysFunctionsDao dao, PwSysFunctionsConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public int batchUpdateAsRequisition(PwSysFunctionsBatchUpdateAsRequisitionParam param) {
        PwSysFunctionsBatchUpdateAsRequisitionArg arg = new PwSysFunctionsBatchUpdateAsRequisitionArg();
        arg.setCorpCode(param.getCorpCode());
        arg.setIds(param.getIds());
        arg.setModifyBy(param.getModifyBy());

        return dao.batchUpdateAsRequisition(arg);
    }
}
