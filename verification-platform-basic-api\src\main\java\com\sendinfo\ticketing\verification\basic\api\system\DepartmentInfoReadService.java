package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.model.system.DepartmentInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/16 16:39
 */
public interface DepartmentInfoReadService {

    /**
     * 根据父级ID查询部门信息
     *
     * @param parentId 父级ID
     * @param corpCode 企业编码
     * @return 部门信息
     */
    ResultModel<List<DepartmentInfo>> queryByParentId(@NotNull Long parentId, @NotEmpty String corpCode);
}
