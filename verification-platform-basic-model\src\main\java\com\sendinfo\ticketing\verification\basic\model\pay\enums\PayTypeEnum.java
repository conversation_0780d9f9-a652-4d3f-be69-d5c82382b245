package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付类型枚举
 * 定义支付方式类型：现金类支付、接口类支付（主扫/被扫）、自定义POS等
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum PayTypeEnum {
    CASH_PAYMENT("0", "现金类支付"),
    INTERFACE_PAYMENT_MERCHANT_SCAN("1", "接口类支付_商户主扫"),
    INTERFACE_PAYMENT_MERCHANT_SCANNED("2", "接口类支付_商户被扫"),
    CUSTOM_POS_SWIPE("4", "自定义POS刷卡"),
    CUSTOM_POS_SCAN("5", "自定义POS扫码");

    @JsonValue
    private final String code;
    private final String description;

    PayTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<String, PayTypeEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(PayTypeEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 支付类型编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static PayTypeEnum ofCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 