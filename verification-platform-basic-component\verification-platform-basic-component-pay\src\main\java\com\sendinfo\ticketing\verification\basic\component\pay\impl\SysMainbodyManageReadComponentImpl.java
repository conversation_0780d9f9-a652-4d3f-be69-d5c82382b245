package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.SysMainbodyManageReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.SysMainbodyManageConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManage;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 经营主体支付配置读取组件实现
 * 使用DaoBased支持接口实现标准的CRUD操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyManageReadComponent")
@Getter
public class SysMainbodyManageReadComponentImpl implements
        SysMainbodyManageReadComponent,
        DaoBasedSingleRead<Long, SysMainbodyManageQueryParam, SysMainbodyManage, SysMainbodyManageDO>,
        DaoBasedListRead<Long, SysMainbodyManageQueryParam, SysMainbodyManage, SysMainbodyManageDO, SysMainbodyManageQueryArg>,
        DaoBasedCountRead<Long, SysMainbodyManageQueryParam, SysMainbodyManage, SysMainbodyManageQueryArg> {

    private final SysMainbodyManageDao dao;
    private final SysMainbodyManageConverter converter;

    public SysMainbodyManageReadComponentImpl(SysMainbodyManageDao dao, SysMainbodyManageConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
}