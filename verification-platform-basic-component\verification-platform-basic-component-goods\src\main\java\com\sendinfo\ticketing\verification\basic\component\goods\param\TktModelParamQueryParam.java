package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:12
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class TktModelParamQueryParam extends AbstractQueryParam {
    private String corpCode;
    private String title;
    private String modelType;
    private Integer modelKind;
    private String useFlag;
    private String modelCode;
}
