package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.customer.request.DirectorInfoQueryByTravelIdRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.DirectorInfoReadService;
import com.sendinfo.ticketing.verification.basic.model.customer.DirectorInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/14 10:46
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class DirectorInfoReadServiceImplTest {

    @Autowired
    private DirectorInfoReadService directorInfoReadService;

//    @Test
    public void testQueryByTravelIdSuccess() {

        DirectorInfoQueryByTravelIdRequest request = new DirectorInfoQueryByTravelIdRequest();
        request.setCorpCode("0001");
        request.setTravelId(5L);

        ResultModel<List<DirectorInfo>> resultModel = directorInfoReadService.queryByTravelId(request);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertTrue(resultModel.getModel().isEmpty());
    }

}
