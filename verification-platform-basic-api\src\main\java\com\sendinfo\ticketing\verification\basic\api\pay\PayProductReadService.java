package com.sendinfo.ticketing.verification.basic.api.pay;

import com.sendinfo.ticketing.verification.basic.api.pay.request.PayProductQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.pay.PayProduct;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 支付产品读服务接口
 * <AUTHOR>
 */
public interface PayProductReadService {

	/**
	 * 查询支付产品
	 *
	 * <AUTHOR>
	 * @param request
	 * @return
	 */
	ResultModel<List<PayProduct>> queryPayProducts(PayProductQueryRequest request);
} 