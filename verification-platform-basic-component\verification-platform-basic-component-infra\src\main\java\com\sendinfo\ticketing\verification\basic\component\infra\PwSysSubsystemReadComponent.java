package com.sendinfo.ticketing.verification.basic.component.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.model.infra.PwSysSubsystem;

/**
 * 子系统管理查询组件接口，提供查询子系统的功能
 *
 * <AUTHOR> 2025-07-24 15:40:00
 */
public interface PwSysSubsystemReadComponent {

    /**
     * 查询所有启用且未删除的子系统
     *
     * @return 子系统列表
     */
    List<PwSysSubsystem> queryAllInUseList(String corpCode);

}