package com.sendinfo.ticketing.verification.basic.component.pay;

import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.common.component.UpdateComponent;

/**
 * 交易记录更新组件接口
 *
 * @since 1.0.0
 */
public interface TransInfoUpdateComponent extends UpdateComponent<TransInfoUpdateParam, TransInfo> {
} 