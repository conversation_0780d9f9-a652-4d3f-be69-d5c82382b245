package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.park.TktParkZoneReadService;
import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkZoneQueryCondition;
import com.sendinfo.ticketing.verification.basic.bootstrap.VerificationPlatformBasicBootstrap;
import com.sendinfo.ticketing.verification.basic.model.park.TktParkZone;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * <AUTHOR> Name]
 * @since 2025/6/19 17:15
 **/
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class TktParkZoneReadServiceImplTest {

    @Autowired
    private TktParkZoneReadService tktParkZoneReadService;

//    @Test
    public void testSearchParkZoneSuccess() {
        // Arrange
        PageRequest<TktParkZoneQueryCondition> pageRequest = new PageRequest<>();
        pageRequest.setPageSize(10);
        pageRequest.setCurrentPage(1);
        TktParkZoneQueryCondition condition = new TktParkZoneQueryCondition();
        condition.setCorpCode("0001");
        pageRequest.setCondition(condition);
        
        // Act
        PageResultModel<TktParkZone> result = tktParkZoneReadService.searchParkZone(pageRequest);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertFalse(result.getModelList().isEmpty());
    }

//    @Test
    public void testQueryParkZoneByParkCodeAndCorpCodeSuccess() {
        // Arrange
        String parkCode = "00020001";
        String corpCode = "0001";
        
        // Act
        ResultModel<TktParkZone> result = tktParkZoneReadService.queryParkZoneByParkCodeAndCorpCode(parkCode, corpCode);
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
    }

//    @Test
    public void testQueryParkZoneByIdSuccess() {
        // Arrange
        Long id = 7465L;
        
        // Act
        ResultModel<TktParkZone> result = tktParkZoneReadService.queryParkZoneById(id, "0001");
        
        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getModel());
    }
}
