package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;

/**
 * 商户渠道配置查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class MainbodyChanleQueryParam extends AbstractPageQueryParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付渠道ID
     */
    private Long payChanleId;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 备注
     */
    private String remark;
} 