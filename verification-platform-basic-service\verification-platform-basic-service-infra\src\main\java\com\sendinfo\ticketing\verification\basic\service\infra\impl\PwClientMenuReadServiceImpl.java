/**
 *  客户端菜单查询服务实现
 *
 *  <AUTHOR> 2025-07-25 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.service.infra.impl;

import com.sendinfo.ticketing.verification.basic.api.infra.request.PwClientMenuQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.infra.PwClientMenuReadService;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.PwClientMenuAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.infra.function.QueryPwClientMenuFunction;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户端菜单查询服务实现
 *
 * <AUTHOR> 2025-07-25 15:30:00
 */
@Service("pwClientMenuReadService")
@Slf4j
public class PwClientMenuReadServiceImpl implements PwClientMenuReadService {

    private final FlowAgentBuilder flowAgentBuilder;
    private final QueryPwClientMenuFunction queryPwClientMenuFunction;

    public PwClientMenuReadServiceImpl(FlowAgentBuilder flowAgentBuilder,
            QueryPwClientMenuFunction queryPwClientMenuFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryPwClientMenuFunction = queryPwClientMenuFunction;
    }

    @Override
    public PageResultModel<PwClientMenu> queryTreePageList(PageRequest<PwClientMenuQueryCondition> pageRequest) {
        return flowAgentBuilder
                .<PageRequest<PwClientMenuQueryCondition>, PageResultModel<PwClientMenu>>validateThenChooseBuilder()
                .appendLogicAction(queryPwClientMenuFunction::queryTreePageList)
                .withSuccessfulAction(q -> {
                    List<PwClientMenu> data = q
                            .getAttachment(PwClientMenuAttachmentKey.PW_CLIENT_MENU_TREE_LIST_ATTACHMENT_KEY);
                    int total = q.getAttachment(PwClientMenuAttachmentKey.PW_CLIENT_MENU_DATA_COUNT_ATTACHMENT_KEY);
                    return Results.successPage(data, total, pageRequest.getPageSize(), pageRequest.getCurrentPage());
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToPageResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[PwClientMenuReadServiceImpl] queryTreePageList error, request: {}", pageRequest, th);
                    return Results.failPage(InfraErrorDef.QUERY_CLIENT_MENU_ERROR);
                })
                .rethrowException(VerificationBizRuntimeException.class::isInstance)
                .build()
                .prompt(pageRequest)
                .getResult();
    }

    @Override
    public ResultModel<List<PwClientMenu>> queryHasRequisitionList(String corpCode) {
        return flowAgentBuilder.<String, ResultModel<List<PwClientMenu>>>validateThenChooseBuilder()
                .appendLogicAction(queryPwClientMenuFunction::queryHasRequisitionTreeList)
                .withSuccessfulAction(q -> {
                    List<PwClientMenu> data = q
                            .getAttachment(PwClientMenuAttachmentKey.PW_CLIENT_MENU_TREE_LIST_ATTACHMENT_KEY);
                    return Results.success(data);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[PwClientMenuReadServiceImpl] queryHasRequisitionList error", th);
                    return Results.fail(InfraErrorDef.QUERY_CLIENT_MENU_ERROR);
                })
                .rethrowException(VerificationBizRuntimeException.class::isInstance)
                .build()
                .prompt(corpCode)
                .getResult();
    }
}
