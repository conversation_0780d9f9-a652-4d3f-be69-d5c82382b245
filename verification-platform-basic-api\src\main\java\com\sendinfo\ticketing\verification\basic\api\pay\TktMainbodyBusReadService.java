package com.sendinfo.ticketing.verification.basic.api.pay;

import com.sendinfo.ticketing.verification.basic.api.pay.request.TktMainbodyBusQueryByTicketIdRequest;
import com.sendinfo.ticketing.verification.basic.api.pay.request.TktMainbodyBusQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.pay.TktMainbodyBus;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/13 14:48
 */
public interface TktMainbodyBusReadService {

    /**
     * 查询主体信息列表
     *
     * @param request 条件
     * @return 主体列表
     */
    ResultModel<List<TktMainbodyBus>> queryTktMainbodyBusList(TktMainbodyBusQueryRequest request);


    /**
     * 查询门票的经营主体
     *
     * @param request 查询门票的经营主体参数
     * @return 门票的经营主体
     */
    ResultModel<TktMainbodyBus> queryTktMainbodyBusByTicketId(TktMainbodyBusQueryByTicketIdRequest request);

}
