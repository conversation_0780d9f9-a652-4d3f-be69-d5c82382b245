package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.model.goods.TktModelParam;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:27
 **/
public interface TktModelParamReadService {
    /**
     * 批量查询票型参数
     *
     * @param ids
     * @param corpCode
     * @return
     */
    ResultModel<List<TktModelParam>> batchQueryTicketModelParamByIds(List<Long> ids, String corpCode);
}
