//package com.sendinfo.ticketing.verification.basic.service.park.impl;
//
//import com.alibaba.fastjson.JSON;
//import com.sendinfo.ticketing.verification.basic.api.customer.request.DirectorInfoQueryByTravelIdRequest;
//import com.sendinfo.ticketing.verification.basic.api.customer.request.GuideFuzzyQueryRequest;
//import com.sendinfo.ticketing.verification.basic.api.customer.request.SalesmanInfoQueryEnabledByIdsRequest;
//import com.sendinfo.ticketing.verification.basic.api.customer.request.SalesmanRelationTravelQueryByTravelIdRequest;
//import com.sendinfo.ticketing.verification.basic.api.customer.DirectorInfoReadService;
//import com.sendinfo.ticketing.verification.basic.api.customer.GuideReadService;
//import com.sendinfo.ticketing.verification.basic.api.customer.SalesmanInfoReadService;
//import com.sendinfo.ticketing.verification.basic.api.customer.SalesmanRelationTravelReadService;
//import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerInfoQueryRequest;
//import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerPayInfoQueryRequest;
//import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerServiceCategoryQueryRequest;
//import com.sendinfo.ticketing.verification.basic.api.goods.ExplainerInfoReadService;
//import com.sendinfo.ticketing.verification.basic.api.goods.ExplainerPayInfoReadService;
//import com.sendinfo.ticketing.verification.basic.api.goods.ExplainerServiceCategoryReadService;
//import com.sendinfo.ticketing.verification.basic.api.park.SysTenementInfoReadService;
//import com.sendinfo.ticketing.verification.basic.api.park.TktParkGateReadService;
//import com.sendinfo.ticketing.verification.basic.api.park.TktParkZoneReadService;
//import com.sendinfo.ticketing.verification.basic.api.park.request.SysTenementInfoCondition;
//import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkGateQueryCondition;
//import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkGateQueryRequest;
//import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkZoneQueryCondition;
//import com.sendinfo.ticketing.verification.basic.api.system.request.SellerUserInfoQueryRequest;
//import com.sendinfo.ticketing.verification.basic.api.system.AreaReadService;
//import com.sendinfo.ticketing.verification.basic.api.system.DepartmentInfoReadService;
//import com.sendinfo.ticketing.verification.basic.api.system.UserInfoReadService;
//import com.sendinfo.ticketing.verification.basic.bootstrap.VerificationPlatformBasicBootstrap;
//import com.sendinfo.ticketing.verification.basic.model.customer.DirectorInfo;
//import com.sendinfo.ticketing.verification.basic.model.customer.Guide;
//import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanInfo;
//import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanRelationTravel;
//import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerInfo;
//import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerPayInfo;
//import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerServiceCategory;
//import com.sendinfo.ticketing.verification.basic.model.park.SysTenementInfo;
//import com.sendinfo.ticketing.verification.basic.model.park.TktParkGate;
//import com.sendinfo.ticketing.verification.basic.model.park.TktParkZone;
//import com.sendinfo.ticketing.verification.basic.model.system.Area;
//import com.sendinfo.ticketing.verification.basic.model.system.DepartmentInfo;
//import com.sendinfo.ticketing.verification.basic.model.system.UserInfo;
//import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
//import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
//import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.util.List;
//import java.util.Set;
//
//import static org.junit.Assert.*;
//import static org.junit.Assert.assertFalse;
//
///**
// * <AUTHOR>
// * @since 2025/7/20 10:18
// **/
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
//public class AllServiceTest {
//
//    @Autowired
//    private AreaReadService areaReadService;
//
//    @Test
//    public void testQueryAreaListByParentCodeSuccess() {
//
//        String parentCode = "110000";
//
//        ResultModel<List<Area>> resultModel = areaReadService.queryAreaListByParentCode(parentCode);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertFalse(resultModel.getModel().isEmpty());
//    }
//
//    @Test
//    public void testQueryAllAreaListSuccess() {
//
//
//        ResultModel<List<Area>> resultModel = areaReadService.queryAllAreaList();
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertFalse(resultModel.getModel().isEmpty());
//    }
//
//
//    @Autowired
//    private DepartmentInfoReadService departmentInfoReadService;
//
//    @Test
//    public void testQueryByParentIdSuccess() {
//
//        ResultModel<List<DepartmentInfo>> resultModel = departmentInfoReadService.queryByParentId(16L, "0001");
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertFalse(resultModel.getModel().isEmpty());
//    }
//
//    @Autowired
//    private DirectorInfoReadService directorInfoReadService;
//
//    @Test
//    public void testQueryByTravelIdSuccess() {
//
//        DirectorInfoQueryByTravelIdRequest request = new DirectorInfoQueryByTravelIdRequest();
//        request.setCorpCode("0001");
//        request.setTravelId(5L);
//
//        ResultModel<List<DirectorInfo>> resultModel = directorInfoReadService.queryByTravelId(request);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertTrue(resultModel.getModel().isEmpty());
//    }
//
//
//    @Autowired
//    private ExplainerInfoReadService explainerInfoReadService;
//
//    @Test
//    public void queryExplainerListTest() {
//
//        // 构造请求参数
//        ExplainerInfoQueryRequest request = new ExplainerInfoQueryRequest();
//        request.setCorpCode("0001");
//        request.setUseFlag("T");
//        request.setStatus("1");
//
//        // 调用远程服务
//        try {
//            ResultModel<List<ExplainerInfo>> resultModel =
//                    explainerInfoReadService.queryExplainerList(request);
//            if (resultModel.isSuccess()) {
//                System.out.println(JSON.toJSONString(resultModel.getModel()));
//            }
//        } catch (Exception e) {
//            System.err.println("调用失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Autowired
//    private ExplainerPayInfoReadService explainerPayInfoReadService;
//
//    @Test
//    public void queryExplainerListTest2() {
//
//        // 构造请求参数
//        ExplainerPayInfoQueryRequest request = new ExplainerPayInfoQueryRequest();
//        request.setCorpCode("0001");
//        request.setUseFlag("T");
//
//        // 调用远程服务
//        try {
//            ResultModel<List<ExplainerPayInfo>> resultModel =
//                    explainerPayInfoReadService.queryPayInfoList(request);
//            if (resultModel.isSuccess()) {
//                System.out.println(JSON.toJSONString(resultModel.getModel()));
//            }
//        } catch (Exception e) {
//            System.err.println("调用失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Autowired
//    private ExplainerServiceCategoryReadService explainerServiceCategoryReadService;
//
//    @Test
//    public void queryExplainerListTest3() {
//
//        // 构造请求参数
//        ExplainerServiceCategoryQueryRequest request = new ExplainerServiceCategoryQueryRequest();
//        request.setCorpCode("0001");
//        request.setUseFlag("T");
//
//        // 调用远程服务
//        try {
//            ResultModel<List<ExplainerServiceCategory>> resultModel =
//                    explainerServiceCategoryReadService.queryServiceCategoryList(request);
//            if (resultModel.isSuccess()) {
//                System.out.println(JSON.toJSONString(resultModel.getModel()));
//            }
//        } catch (Exception e) {
//            System.err.println("调用失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    @Autowired
//    private GuideReadService guideReadService;
//
//    @Test
//    public void testFuzzyQueryGuideListSuccess() {
//        GuideFuzzyQueryRequest request = new GuideFuzzyQueryRequest();
//
//        request.setCorpCode("0001");
//        request.setFuzzyName("测试");
//        ResultModel<List<Guide>> resultModel = guideReadService.fuzzyQueryGuideList(request);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertFalse(resultModel.getModel().isEmpty());
//    }
//
//    @Test
//    public void testFuzzyQueryGuideListEmptySuccess() {
//        GuideFuzzyQueryRequest request = new GuideFuzzyQueryRequest();
//
//        request.setCorpCode("0001");
//        request.setFuzzyName("测试12312323123");
//        ResultModel<List<Guide>> resultModel = guideReadService.fuzzyQueryGuideList(request);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertTrue(resultModel.getModel().isEmpty());
//    }
//
//    @Autowired
//    private SalesmanInfoReadService salesmanInfoReadService;
//
//    @Test
//    public void testQueryEnabledByIdsSuccess() {
//
//        SalesmanInfoQueryEnabledByIdsRequest request = new SalesmanInfoQueryEnabledByIdsRequest();
//        request.setCorpCode("0001");
//        request.setIds(Set.of(9L, 10L, 11L));
//
//        ResultModel<List<SalesmanInfo>> resultModel = salesmanInfoReadService.queryEnabledByIds(request);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//    }
//
//    @Autowired
//    private SalesmanRelationTravelReadService salesmanRelationTravelReadService;
//
//    @Test
//    public void testQueryByTravelIdSuccess2() {
//
//        SalesmanRelationTravelQueryByTravelIdRequest request = new SalesmanRelationTravelQueryByTravelIdRequest();
//        request.setCorpCode("0001/10241597/");
//        request.setTravelId(71733L);
//
//        ResultModel<List<SalesmanRelationTravel>> resultModel = salesmanRelationTravelReadService.queryByTravelId(request);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertFalse(resultModel.getModel().isEmpty());
//    }
//
//    @Autowired
//    private SysTenementInfoReadService sysTenementInfoReadService;
//
//    private String corpCode = "0001";
//
//    @Test
//    public void testSearchSysTenementInfoSuccess() {
//        // Arrange
//        PageRequest<SysTenementInfoCondition> pageRequest = new PageRequest<>();
//        pageRequest.setPageSize(10);
//        pageRequest.setCurrentPage(1);
//        SysTenementInfoCondition condition = new SysTenementInfoCondition();
//        condition.setCorpCode(corpCode);
//        pageRequest.setCondition(condition);
//
//        // Act
//        PageResultModel<SysTenementInfo> result = sysTenementInfoReadService.searchSysTenementInfo(pageRequest);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//    }
//
//
//    @Test
//    public void testQuerySysTenementInfoByCorpCodeSuccess() {
//
//        // Act
//        ResultModel<List<SysTenementInfo>> result = sysTenementInfoReadService.querySysTenementInfoByCorpCode(corpCode);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertFalse(result.getModel().isEmpty());
//    }
//
//    @Autowired
//    private TktParkGateReadService tktParkGateReadService;
//
//    @Test
//    public void testSearchParkGateSuccess() {
//        // Arrange
//        PageRequest<TktParkGateQueryCondition> pageRequest = new PageRequest<>();
//        pageRequest.setPageSize(10);
//        pageRequest.setCurrentPage(1);
//        TktParkGateQueryCondition condition = new TktParkGateQueryCondition();
//        condition.setCorpCode("0001");
//        pageRequest.setCondition(condition);
//
//        // Act
//        PageResultModel<TktParkGate> result = tktParkGateReadService.searchParkGate(pageRequest);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertFalse(result.getModelList().isEmpty());
//    }
//
//    @Test
//    public void testQueryParkGateListSuccess() {
//        // Arrange
//        Long parkZoneId = 51L;
//        String corpCode = "0001";
//
//        // Act
//        ResultModel<List<TktParkGate>> result = tktParkGateReadService.queryParkGateList(parkZoneId, corpCode);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertFalse(result.getModel().isEmpty());
//    }
//
//    @Test
//    public void testQueryParkGateSuccess() {
//        // Arrange
//        Long parkGateId = 22L;
//        String corpCode = "0001";
//
//        // Act
//        ResultModel<TktParkGate> result = tktParkGateReadService.queryParkGate(parkGateId, corpCode);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertNotNull(result.getModel());
//    }
//
//    @Test
//    public void testQueryParkGateByGateNoSuccess() {
//        // Arrange
//        TktParkGateQueryRequest request = new TktParkGateQueryRequest();
//        request.setGateNo(7);
//        request.setParkZoneId(51L);
//        request.setCorpCode("0001");
//
//        // Act
//        ResultModel<TktParkGate> result = tktParkGateReadService.queryParkGateByGateNo(request);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertNotNull(result.getModel());
//    }
//
//    @Autowired
//    private TktParkZoneReadService tktParkZoneReadService;
//
//    @Test
//    public void testSearchParkZoneSuccess() {
//        // Arrange
//        PageRequest<TktParkZoneQueryCondition> pageRequest = new PageRequest<>();
//        pageRequest.setPageSize(10);
//        pageRequest.setCurrentPage(1);
//        TktParkZoneQueryCondition condition = new TktParkZoneQueryCondition();
//        condition.setCorpCode("0001");
//        pageRequest.setCondition(condition);
//
//        // Act
//        PageResultModel<TktParkZone> result = tktParkZoneReadService.searchParkZone(pageRequest);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertFalse(result.getModelList().isEmpty());
//    }
//
//    @Test
//    public void testQueryParkZoneByParkCodeAndCorpCodeSuccess() {
//        // Arrange
//        String parkCode = "00020001";
//        String corpCode = "0001";
//
//        // Act
//        ResultModel<TktParkZone> result = tktParkZoneReadService.queryParkZoneByParkCodeAndCorpCode(parkCode, corpCode);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertNotNull(result.getModel());
//    }
//
//    @Test
//    public void testQueryParkZoneByIdSuccess() {
//        // Arrange
//        Long id = 7465L;
//
//        // Act
//        ResultModel<TktParkZone> result = tktParkZoneReadService.queryParkZoneById(id);
//
//        // Assert
//        assertNotNull(result);
//        assertTrue(result.isSuccess());
//        assertNotNull(result.getModel());
//    }
//
//    @Autowired
//    private UserInfoReadService userInfoReadService;
//
//    @Test
//    public void testQueryByIdSuccess() {
//
//        ResultModel<UserInfo> resultModel = userInfoReadService.queryById(1L);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertNotNull(resultModel.getModel());
//    }
//
//    @Test
//    public void testQueryListBySellerUserInfoSuccess() {
//        SellerUserInfoQueryRequest request = new SellerUserInfoQueryRequest();
//        request.setRealName("平台管理员");
//        request.setDeptIdSet(Set.of(16L, 108L));
//        request.setCorpCode("0001");
//        request.setUserId(2L);
//        ResultModel<List<UserInfo>> resultModel = userInfoReadService.queryListBySellerUserInfo(request);
//
//        assertNotNull(resultModel);
//        assertTrue(resultModel.isSuccess());
//        assertFalse(resultModel.getModel().isEmpty());
//    }
//}
