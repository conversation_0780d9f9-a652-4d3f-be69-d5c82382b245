package com.sendinfo.ticketing.verification.basic.api.customer;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoDeptQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/24 09:31
 */
public interface TravelInfoReadService {

    /**
     * 查询旅行社信息
     *
     * @param id 主键
     * @return 旅行社信息
     */
    ResultModel<TravelInfo> queryTravelInfo(@NotNull Long id);

    /**
     * 批量查询旅行社信息
     *
     * @param ids 主键集合
     * @return 旅行社信息
     */
    ResultModel<List<TravelInfo>> batchQueryByIds(@NotEmpty Set<Long> ids);

    /**
     * 查询旅行社信息列表
     *
     * @param queryRequest  旅行社信息查询请求对象
     * @return  旅行社信息
     */
    ResultModel<List<TravelInfo>> queryTravelInfoList(TravelInfoQueryRequest queryRequest);

    /**
     * 查询角色旅行社信息列表
     *
     * @param queryRequest  旅行社信息查询请求对象
     * @return  旅行社信息
     */
    ResultModel<List<TravelInfo>> queryRoleTravelInfoList(TravelInfoRoleQueryRequest queryRequest);

    /**
     * 查询组织机构旅行社信息列表
     *
     * @param queryRequest  旅行社信息查询请求对象
     * @return  旅行社信息
     */
    ResultModel<List<TravelInfo>> queryDeptTravelInfoList(TravelInfoDeptQueryRequest queryRequest);
}
