package com.sendinfo.ticketing.verification.basic.component.account.impl;

import com.sendinfo.ticketing.verification.basic.component.account.CapitalAccountCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.account.converter.CapitalAccountConverter;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountCreateParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.basic.repository.account.dao.CapitalAccountDao;
import com.sendinfo.ticketing.verification.basic.repository.account.dataobject.CapitalAccountDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 资金账户创建组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Component("capitalAccountCreateComponent")
public class CapitalAccountCreateComponentImpl implements CapitalAccountCreateComponent,
        DaoBasedSingleCreate<Long, CapitalAccountCreateParam, CapitalAccountDO> {
    private final CapitalAccountDao dao;
    private final CapitalAccountConverter converter;

    public CapitalAccountCreateComponentImpl(CapitalAccountDao dao, CapitalAccountConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public CapitalAccount createCapitalAccount(CapitalAccountCreateParam createParam) {
        CapitalAccountDO capitalAccountDO = converter.c_p2d(createParam);
        dao.insert(capitalAccountDO);
        return converter.r_d2m(capitalAccountDO);
    }
}