package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.system.AreaReadService;
import com.sendinfo.ticketing.verification.basic.model.system.Area;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/14 10:46
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class AreaReadServiceImplTest {

    @Autowired
    private AreaReadService areaReadService;

//    @Test
    public void testQueryAreaListByParentCodeSuccess() {

        String parentCode = "110000";

        ResultModel<List<Area>> resultModel = areaReadService.queryAreaListByParentCode(parentCode);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertFalse(resultModel.getModel().isEmpty());
    }

//    @Test
    public void testQueryAllAreaListSuccess() {


        ResultModel<List<Area>> resultModel = areaReadService.queryAllAreaList();

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertFalse(resultModel.getModel().isEmpty());
    }

}
