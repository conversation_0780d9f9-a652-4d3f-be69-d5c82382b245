package com.sendinfo.ticketing.verification.basic.component.customer.converter;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelTaxCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelTaxQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.customer.mapper.TravelTaxMapper;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelTax;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelTaxQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelInfoDO;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelTaxDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

/**
 * travel_tax信息转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("travelTaxConverter")
public class TravelTaxConverter implements
        ReadDo2ModelConverter<TravelTaxDO, TravelTax>,
        ReadParam2ArgConverter<TravelTaxQueryParam, TravelTaxQueryArg>,
        ReadReq2ParamConverter<TravelTaxQueryRequest, TravelTaxQueryParam>,
        CreateParam2DoConverter<TravelTaxCreateParam, TravelTaxDO>,
        CreateReq2ParamConverter<TravelTaxCreateRequest, TravelTaxCreateParam> {
    @Override
    public TravelTax r_d2m(TravelTaxDO dataObject) {
        return TravelTaxMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public TravelTaxQueryArg r_p2a(TravelTaxQueryParam param) {
        return TravelTaxMapper.INSTANCE.convert(param);
    }

    @Override
    public TravelTaxQueryParam r_r2p(TravelTaxQueryRequest queryRequest) {
        return TravelTaxMapper.INSTANCE.convert(queryRequest);
    }

    @Override
    public TravelTaxDO c_p2d(TravelTaxCreateParam createParam) {
        return TravelTaxMapper.INSTANCE.convert(createParam);
    }

    @Override
    public TravelTaxCreateParam c_r2p(TravelTaxCreateRequest createRequest) {
        return TravelTaxMapper.INSTANCE.convert(createRequest);
    }
}