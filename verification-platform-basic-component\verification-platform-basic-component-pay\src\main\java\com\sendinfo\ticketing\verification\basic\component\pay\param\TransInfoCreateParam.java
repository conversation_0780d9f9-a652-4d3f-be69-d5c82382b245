package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.model.pay.enums.BusinessTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.TransTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录创建参数
 *
 * @since 1.0.0
 */
@Data
@ToString(callSuper = true)
public class TransInfoCreateParam extends AbstractCreateParam {
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 内部交易号
     */
    private String transNo;
    /**
     * 交易类型：0支付1退款
     */
    private TransTypeEnum transType;
    /**
     * 交易ID
     */
    private Long tradeId;
    /**
     * 客户代码+交易ID号
     */
    private String tradeCode;
    /**
     * 支付方式中文说明
     */
    private String payAway;
    /**
     * 支付网关类型
     */
    private Integer gateway;
    /**
     * 网关交易号
     */
    private String gatewayTransNo;
    /**
     * 业务类型：0:门票1：剧院2：滑雪场4:一卡通6:租赁13:存包柜
     */
    private BusinessTypeEnum businessType;
    /**
     * 交易金额
     */
    private BigDecimal payAmount;
    /**
     * 登录ID
     */
    private Long loginId;
    /**
     * 操作员名
     */
    private String loginName;
    /**
     * 支付状态：0:待支付 1:支付未完成 2:未支付 3:已支付
     */
    private PayStatusEnum payStatus;
    /**
     * 报告时间
     */
    private Date reportTime;
    /**
     * 实际支付时间
     */
    private Date payTime;
    /**
     * 交易报文
     */
    private String reportInfo;
    /**
     * 页面展示支付方式
     */
    private String payTypeView;
    /**
     * 支付总金额
     */
    private BigDecimal payTotal;
    /**
     * 退单支付交易号
     */
    private String refundPayTransno;
    /**
     * 子支付方式
     */
    private String payType;
    /**
     * 主体支付ID
     */
    private Long mainbodyPayid;
    /**
     * 付款信息
     */
    private String payInfo;
    /**
     * 扩展参数
     */
    private String extendParamJson;
    /**
     * 终端号
     */
    private String tid;
    /**
     * 积分数量
     */
    private Integer payIntegralSum;
    /**
     * 积分支付抵扣金额
     */
    private BigDecimal payIntegralTotal;
    /**
     * 其他交易号
     */
    private String otherTransNo;
    /**
     * 结算标识T结算F未结算
     */
    private String settlement;

    /**
     * create by
     */
    private String createBy;

    /**
     * modify by
     */
    private String modifyBy;
} 