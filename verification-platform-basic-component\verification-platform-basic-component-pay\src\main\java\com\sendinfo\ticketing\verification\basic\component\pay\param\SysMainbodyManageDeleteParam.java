package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractDeleteParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置删除参数
 * 用于封装删除经营主体支付配置所需的参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageDeleteParam extends AbstractDeleteParam {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 修改人
     */
    private String modifyBy;
} 