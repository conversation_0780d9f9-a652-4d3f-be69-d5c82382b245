package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.alibaba.nacos.api.exception.NacosException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.infra.TestMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl.PwClientMenuDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwClientMenuDO;

@SpringBootTest(classes = PwClientMenuDaoTest.Config.class)
@EnableAutoConfiguration
@Rollback
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:test-application.properties")
public class PwClientMenuDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private PwClientMenuDao pwClientMenuDao;

    private static final String TEST_CORP_CODE = "TEST_CORP";

    @Configuration
    @Import({ SaasPwMybatisConfig.class })
    static class Config {
        @Bean
        PwClientMenuDao pwClientMenuDao(SqlSessionTemplate sqlSessionTemplate) {
            return new PwClientMenuDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(@Value("${nacos.config.server-addr}") String serverAddr,
                                                                                            @Value("${nacos.config.namespace}") String namespace,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                                                                                            @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout) throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    @Before
    public void setUp() {
        // 初始化测试数据 - 插入10条数据
        for (int i = 1; i <= 10; i++) {
            PwClientMenuDO menu = createTestMenu(i);
            pwClientMenuDao.insert(menu);
        }

        // 初始化测试数据 - 插入10条没有parentId的数据
        for (int i = 11; i <= 20; i++) {
            PwClientMenuDO menu = createTestMenu(i);
            menu.setParentId(null);
            pwClientMenuDao.insert(menu);
        }
    }

    @Test
    public void testInsert() {
        // Given
        PwClientMenuDO menu = createTestMenu(100);

        // When
        pwClientMenuDao.insert(menu);

        // Then
        PwClientMenuDO found = pwClientMenuDao.queryById(menu.getId());
        Assert.assertNotNull(found);
        Assert.assertEquals(menu.getMenuCode(), found.getMenuCode());
        Assert.assertEquals(menu.getMenuName(), found.getMenuName());
        Assert.assertEquals(menu.getCorpCode(), found.getCorpCode());
    }

    @Test
    public void testQueryById() {
        // Given
        PwClientMenuDO menu = createTestMenu(200);
        pwClientMenuDao.insert(menu);

        // When
        PwClientMenuDO found = pwClientMenuDao.queryById(menu.getId());

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(menu.getId(), found.getId());
        Assert.assertEquals(menu.getMenuCode(), found.getMenuCode());
    }

    @Test
    public void testUpdateByArg() {
        // Given
        PwClientMenuDO menu = createTestMenu(300);
        pwClientMenuDao.insert(menu);

        PwClientMenuUpdateArg updateArg = new PwClientMenuUpdateArg();
        updateArg.setId(menu.getId());
        updateArg.setRequisitionFlag(1);
        updateArg.setModifyBy("testUser");

        // When
        pwClientMenuDao.updateByArg(updateArg);

        // Then
        PwClientMenuDO updated = pwClientMenuDao.queryById(menu.getId());
        Assert.assertNotNull(updated);
        Assert.assertEquals(1, updated.getRequisitionFlag().intValue());
    }

    @Test
    public void testSoftDeleteByArg() {
        // Given
        PwClientMenuDO menu = createTestMenu(400);
        pwClientMenuDao.insert(menu);

        PwClientMenuDeleteArg deleteArg = new PwClientMenuDeleteArg();
        deleteArg.setId(menu.getId());
        deleteArg.setModifyBy("testUser");

        // When
        pwClientMenuDao.softDeleteByArg(deleteArg);

        // Then
        PwClientMenuDO deleted = pwClientMenuDao.queryById(menu.getId());
        Assert.assertNull(deleted); // 软删除后查询不到
    }

    @Test
    public void testDeleteByArg() {
        // Given
        PwClientMenuDO menu = createTestMenu(500);
        pwClientMenuDao.insert(menu);

        PwClientMenuDeleteArg deleteArg = new PwClientMenuDeleteArg();
        deleteArg.setId(menu.getId());

        // When
        pwClientMenuDao.deleteByArg(deleteArg);

        // Then
        PwClientMenuDO deleted = pwClientMenuDao.queryById(menu.getId());
        Assert.assertNull(deleted); // 物理删除后查询不到
    }

    @Test
    public void testCountByArg() {

        // Given
        PwClientMenuQueryArg queryArg = new PwClientMenuQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);

        // When
        Integer count = pwClientMenuDao.countByArg(queryArg);

        // Then
        Assert.assertNotNull(count);
        Assert.assertTrue(count >= 10); // 至少有setUp中插入的10条数据
    }

    @Test
    public void testQueryByArg() {
        // Given
        PwClientMenuQueryArg queryArg = new PwClientMenuQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setOffset(0);
        queryArg.setLimit(5);

        // When
        List<PwClientMenuDO> results = pwClientMenuDao.queryByArg(queryArg);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.size() <= 5);
    }

    @Test
    public void testQueryByParentIds() {
        // Given
        List<Long> parentIds = Arrays.asList(1L, 2L, 3L);

        // When
        List<PwClientMenuDO> results = pwClientMenuDao.queryByParentIds(parentIds);

        // Then
        Assert.assertNotNull(results);
        // 验证返回的菜单的父ID都在指定列表中
        for (PwClientMenuDO menu : results) {
            Assert.assertTrue(parentIds.contains(menu.getParentId().longValue()));
        }
    }

    @Test
    public void testQueryHasRequisitionList() {
        // Given - 创建一些已征用的菜单
        PwClientMenuDO requisitionedMenu = createTestMenu(2001);
        requisitionedMenu.setRequisitionFlag(1);
        requisitionedMenu.setCorpCode("TEST_CORP");
        pwClientMenuDao.insert(requisitionedMenu);

        // When
        List<PwClientMenuDO> results = pwClientMenuDao.queryHasRequisitionList("TEST_CORP");

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.size() > 0);
        // 验证所有返回的菜单都是已征用的
        for (PwClientMenuDO menu : results) {
            Assert.assertEquals(Integer.valueOf(1), menu.getRequisitionFlag());
            Assert.assertEquals("TEST_CORP", menu.getCorpCode());
        }
    }

    @Test
    public void testQueryHasRequisitionListWithNullCorpCode() {
        // Given - 创建一些已征用的菜单
        PwClientMenuDO requisitionedMenu = createTestMenu(2002);
        requisitionedMenu.setRequisitionFlag(1);
        requisitionedMenu.setCorpCode("TEST_CORP2");
        pwClientMenuDao.insert(requisitionedMenu);

        // When - 传入null的corpCode
        List<PwClientMenuDO> results = pwClientMenuDao.queryHasRequisitionList(null);

        // Then - 应该返回所有已征用的菜单，不过滤corpCode
        Assert.assertNotNull(results);
        Assert.assertTrue(results.size() > 0);
        // 验证所有返回的菜单都是已征用的
        for (PwClientMenuDO menu : results) {
            Assert.assertEquals(Integer.valueOf(1), menu.getRequisitionFlag());
        }
    }

    private PwClientMenuDO createTestMenu(int index) {
        PwClientMenuDO menu = new PwClientMenuDO();
        menu.setCorpCode(TEST_CORP_CODE);
        menu.setBusinessType(0);
        menu.setMenuName("Test Menu " + index);
        menu.setMenuCode("TEST_MENU_" + index);
        menu.setIconImg("icon_" + index + ".png");
        menu.setTitle("Test Title " + index);
        menu.setMenuUrl("/test/menu/" + index);
        menu.setWpfUrl("/wpf/test/" + index);
        menu.setSysCode("TEST_SYS");
        menu.setSortOrder(index);
        menu.setStatus("T");
        menu.setRemark("Test remark " + index);
        menu.setParentId(index % 5); // 设置父ID为0-4之间的值
        menu.setParentIds("0," + (index % 5));
        menu.setFunctionType("menu");
        menu.setSubsystemId(1);
        menu.setStyleFileUrl("style_" + index + ".css");
        menu.setRequisitionFlag(0);
        menu.setCreateBy("testUser");
        menu.setModifyBy("testUser");
        menu.setDeleted("F");
        return menu;
    }

    @Test
    public void testBatchUpdateAsRequisition() {
        // 准备测试数据：插入3个菜单
        PwClientMenuDO menu1 = createTestMenu(3001);
        PwClientMenuDO menu2 = createTestMenu(3002);
        PwClientMenuDO menu3 = createTestMenu(3003);

        pwClientMenuDao.insert(menu1);
        pwClientMenuDao.insert(menu2);
        pwClientMenuDao.insert(menu3);

        // 验证初始状态：征用标记都是0
        PwClientMenuDO inserted1 = pwClientMenuDao.queryById(menu1.getId());
        PwClientMenuDO inserted2 = pwClientMenuDao.queryById(menu2.getId());
        PwClientMenuDO inserted3 = pwClientMenuDao.queryById(menu3.getId());

        Assert.assertEquals(Integer.valueOf(0), inserted1.getRequisitionFlag());
        Assert.assertEquals(Integer.valueOf(0), inserted2.getRequisitionFlag());
        Assert.assertEquals(Integer.valueOf(0), inserted3.getRequisitionFlag());

        // 执行批量更新：将menu1和menu3标记为已征用
        PwClientMenuBatchUpdateAsRequisitionArg arg = new PwClientMenuBatchUpdateAsRequisitionArg();
        arg.setIds(Arrays.asList(menu1.getId(), menu3.getId()));
        arg.setModifyBy("testUser");

        int updatedCount = pwClientMenuDao.batchUpdateAsRequisition(arg);

        // 验证更新结果
        Assert.assertEquals(2, updatedCount);

        // 验证更新后的状态
        PwClientMenuDO updated1 = pwClientMenuDao.queryById(menu1.getId());
        PwClientMenuDO updated2 = pwClientMenuDao.queryById(menu2.getId());
        PwClientMenuDO updated3 = pwClientMenuDao.queryById(menu3.getId());

        Assert.assertEquals(Integer.valueOf(1), updated1.getRequisitionFlag()); // 已征用
        Assert.assertEquals(Integer.valueOf(0), updated2.getRequisitionFlag()); // 未征用
        Assert.assertEquals(Integer.valueOf(1), updated3.getRequisitionFlag()); // 已征用

        Assert.assertEquals("testUser", updated1.getModifyBy());
        Assert.assertEquals("testUser", updated3.getModifyBy());
    }

    @Test
    public void testBatchUpdateAsRequisitionWithEmptyIds() {
        // 测试空ID列表的情况
        PwClientMenuBatchUpdateAsRequisitionArg arg = new PwClientMenuBatchUpdateAsRequisitionArg();
        arg.setIds(Arrays.asList());
        arg.setModifyBy("testUser");

        int updatedCount = pwClientMenuDao.batchUpdateAsRequisition(arg);

        // 验证没有记录被更新
        Assert.assertEquals(0, updatedCount);
    }
}
