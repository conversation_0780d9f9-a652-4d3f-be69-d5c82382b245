package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.TravelTaxCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.TravelTaxConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxCreateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelTax;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.TravelTaxDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelTaxDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * travel_tax信息创建组件接口实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 17:33
 */
@Component("travelTaxCreateComponent")
@Getter
public class TravelTaxCreateComponentImpl implements TravelTaxCreateComponent,
		DaoBasedSingleCreate<Long, TravelTaxCreateParam, TravelTaxDO> {

	private final TravelTaxDao dao;
	private final TravelTaxConverter converter;

	public TravelTaxCreateComponentImpl(TravelTaxDao dao, TravelTaxConverter converter) {
		this.dao = dao;
		this.converter = converter;
	}

	@Override
	public TravelTax createTravelTax(TravelTaxCreateParam createParam) {
		TravelTaxDO travelTaxDO = converter.c_p2d(createParam);
		dao.insert(travelTaxDO);

		return converter.r_d2m(travelTaxDO);
	}
}
