package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysSubsystemQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysSubsystem;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 子系统管理读取服务接口
 * <AUTHOR>
 */
public interface SysSubsystemReadService {

    /**
     * 查询子系统列表
     *
     * @param request 查询条件
     * @return 子系统列表
     */
    ResultModel<List<SysSubsystem>> querySysSubsystems(SysSubsystemQueryRequest request);
} 