package com.sendinfo.ticketing.verification.basic.api.pay;

import com.sendinfo.ticketing.verification.basic.api.pay.response.TransInfoPayAwayResponse;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 交易记录读取 服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/29 10:42
 */
public interface TransInfoReadService {

	/**
	 * 获取交易记录支付方式列表
	 *
	 * @param orderCode 订单编号
	 * @param corpCode  企业编码
	 * @return  List<TransInfoPayAwayResponse>
	 */
	ResultModel<List<TransInfoPayAwayResponse>> queryTransInfoPayAway(String orderCode, String corpCode);

	/**
	 * 根据订单号查询交易信息
	 *
	 * @param tradeCode 订单号
	 * @param transType 交易类型
	 * @param corpCode  企业编码
	 * @return 交易记录
	 */
	ResultModel<List<TransInfo>> queryListByTradeCodeAndTransType(@NotEmpty String tradeCode, @NotNull Integer transType, @NotEmpty String corpCode);
}
