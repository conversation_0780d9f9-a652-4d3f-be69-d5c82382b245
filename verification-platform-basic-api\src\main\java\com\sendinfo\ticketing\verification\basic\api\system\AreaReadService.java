package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.model.system.Area;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/24 17:31
 */
public interface AreaReadService {

    /**
     * 根据父区划编码查询行政区划列表
     *
     * @param parentCode 父区划编码
     * @return 行政区划列表
     */
    ResultModel<List<Area>> queryAreaListByParentCode(@NotEmpty String parentCode);

    /**
     * 查询全部行政区划列表
     *
     * @return 全部行政区划列表
     */
    ResultModel<List<Area>> queryAllAreaList();
}
