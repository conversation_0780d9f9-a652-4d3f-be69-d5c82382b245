package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体更新参数
 * 用于封装更新经营主体所需的参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业码
     */
    private String corpCode;
    
    /**
     * 经营主体名称
     */
    private String mainbodyName;

    /**
     * 经营主体简称
     */
    private String mainbodyShortName;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkTel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 经营主体编号
     */
    private String mainbodyNumber;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 修改人
     */
    private String modifyBy;

} 