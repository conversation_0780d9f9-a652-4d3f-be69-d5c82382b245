package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 支付状态枚举
 * 0=待支付, 1=支付未完成, 2=未支付, 3=已支付
 */
@Getter
public enum PayStatusEnum {
    WAITING("0", "待支付"),
    INCOMPLETE("1", "支付未完成"),
    UNPAID("2", "未支付"),
    PAID("3", "已支付");

    @JsonValue
    private final String code;
    private final String description;

    PayStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<String, PayStatusEnum> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(PayStatusEnum::getCode, Function.identity()));

    @JsonCreator
    public static PayStatusEnum ofCode(String code) {
        return code == null ? null : CODE_MAP.get(code);
    }
} 