package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.PayChanleReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.PayChanleConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.PayChanle;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.PayChanleDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 支付渠道读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("payChanleReadComponent")
@Getter
public class PayChanleReadComponentImpl implements
        PayChanleReadComponent,
        DaoBasedSingleRead<Long, PayChanleQueryParam, PayChanle, PayChanleDO>,
        DaoBasedListRead<Long, PayChanleQueryParam, PayChanle, PayChanleDO, PayChanleQueryArg>,
        DaoBasedCountRead<Long, PayChanleQueryParam, PayChanle, PayChanleQueryArg> {

    private final PayChanleDao dao;
    private final PayChanleConverter converter;

    public PayChanleReadComponentImpl(PayChanleDao dao, PayChanleConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 