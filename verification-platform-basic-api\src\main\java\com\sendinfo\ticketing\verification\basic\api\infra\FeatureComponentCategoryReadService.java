package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.infra.FeatureComponentCategory;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 组件分类查询服务
 * 
 * <AUTHOR> 2025-07-22 11:45:32
 */
public interface FeatureComponentCategoryReadService {

    /**
     * 分页查询组件分类
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<FeatureComponentCategory> queryPageList(
            PageRequest<FeatureComponentCategoryQueryCondition> pageRequest);

    /**
     * 根据ID查询组件分类
     *
     * @param id 分类ID
     * @return 组件分类
     */
    ResultModel<FeatureComponentCategory> queryFeatureComponentCategoryById(Long id);


    /**
     * 查询所有分类列表
     *
     * @param corpCode 企业编码
     * @return 分类列表
     */
    ResultModel<List<FeatureComponentCategory>> queryAllList(String corpCode);
}
