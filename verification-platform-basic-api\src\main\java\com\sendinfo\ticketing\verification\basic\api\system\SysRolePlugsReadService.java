package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysRolePlugsQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysRolePlugs;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统菜单角色权限读取服务接口
 * 对应表：sys_roleplugs
 * 方法、注释、参数、返回值类型严格对齐 system/api 层
 * <AUTHOR>
 */
public interface SysRolePlugsReadService {

    /**
     * 根据主键ID查询系统菜单角色权限
     *
     * @param id 主键ID
     * @return 系统菜单角色权限信息
     */
    ResultModel<SysRolePlugs> queryById(@NotNull Long id);

    /**
     * 根据条件查询系统菜单角色权限列表
     *
     * @param request 查询条件
     * @return 系统菜单角色权限列表
     */
    ResultModel<List<SysRolePlugs>> queryList(SysRolePlugsQueryRequest request);

    /**
     * 根据系统角色id集合和权限类型查询系统菜单角色权限列表
     *
     * @param sysRoleIds 系统角色Id集合
     * @return 系统菜单角色权限列表
     */
    ResultModel<List<SysRolePlugs>> queryListBySysRoleIdRoleModelType(List<Long> sysRoleIds, Integer roleModelType);
} 