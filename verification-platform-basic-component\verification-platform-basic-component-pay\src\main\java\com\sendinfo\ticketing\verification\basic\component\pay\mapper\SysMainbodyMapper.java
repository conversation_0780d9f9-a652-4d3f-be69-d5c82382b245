package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbody;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 经营主体对象映射器
 * 使用MapStruct自动生成对象转换实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SysMainbodyMapper {

    SysMainbodyMapper INSTANCE = Mappers.getMapper(SysMainbodyMapper.class);

    /**
     * 创建参数转换为数据对象
     */
    SysMainbodyDO convert(SysMainbodyCreateParam createParam);

    /**
     * 查询参数转换为查询参数
     */
    SysMainbodyQueryArg convert(SysMainbodyQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     */
    SysMainbodyUpdateArg convert(SysMainbodyUpdateParam updateParam);

    /**
     * 数据对象转换为模型对象
     */
    SysMainbody convert(SysMainbodyDO dataObject);
} 