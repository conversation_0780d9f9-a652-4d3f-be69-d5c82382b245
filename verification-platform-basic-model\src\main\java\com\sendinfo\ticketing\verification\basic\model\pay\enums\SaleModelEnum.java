package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 售票模式枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum SaleModelEnum {
    /**
     * 正常出票
     */
    NORMAL_SALE(1, "正常出票"),

    /**
     * 预售票
     */
    PRESALE(2, "预售票"),

    /**
     * 电子商务票
     */
    E_COMMERCE(3, "电子商务票"),

    /**
     * 手工票补录
     */
    MANUAL_ENTRY(4, "手工票补录"),

    /**
     * 剧院售票
     */
    THEATER_SALE(5, "剧院售票"),

    /**
     * 自助机
     */
    SELF_SERVICE(6, "自助机"),

    /**
     * 扫码入园
     */
    SCAN_ENTRY(7, "扫码入园");

    /**
     * 编码
     */
    @JsonValue
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    SaleModelEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    @JsonCreator
    public static SaleModelEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SaleModelEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Unknown sale model code: " + code);
    }
} 