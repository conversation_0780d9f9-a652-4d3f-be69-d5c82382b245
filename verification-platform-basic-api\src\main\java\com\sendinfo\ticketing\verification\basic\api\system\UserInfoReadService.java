package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.SellerUserInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.UserInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/16 16:12
 */
public interface UserInfoReadService {

    /**
     * 根据id查询用户信息
     *
     * @param id 用户主键
     * @return 用户信息
     */
    ResultModel<UserInfo> queryById(@NotNull Long id);

    /**
     * 根据id查询用户信息
     *
     * @param id 用户主键
     * @param corpCode 企业编码
     * @return 用户信息
     */
    ResultModel<UserInfo> queryById(@NotNull Long id, @NotBlank String corpCode);

    /**
     * 根据id查询用户信息
     *
     * @param accName 用户名
     * @param corpCode 企业编码
     * @return 用户信息
     */
    ResultModel<UserInfo> queryByAccName(@NotBlank String accName, @NotBlank String corpCode);

    /**
     * 根据姓名和部门id查询用户信息
     *
     * @param request 入参
     * @return 用户信息列表
     */
    ResultModel<List<UserInfo>> queryListBySellerUserInfo(SellerUserInfoQueryRequest request);
}
