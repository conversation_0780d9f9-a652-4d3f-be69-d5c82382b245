package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.alibaba.nacos.api.exception.NacosException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.AbstractTransactionalJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import com.sendinfo.ticketing.verification.basic.repository.common.config.SaasPwMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.common.sharding.DynamicDataSourceMappingConfigPullStorage;
import com.sendinfo.ticketing.verification.basic.repository.infra.TestMybatisConfig;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.impl.PwSysFunctionsDaoImpl;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysFunctionsDO;

@SpringBootTest(classes = PwSysFunctionsDaoTest.Config.class)
@EnableAutoConfiguration
@Rollback
@RunWith(SpringRunner.class)
@TestPropertySource(locations = "classpath:test-application.properties")
public class PwSysFunctionsDaoTest extends AbstractTransactionalJUnit4SpringContextTests {

    @Autowired
    private PwSysFunctionsDao pwSysFunctionsDao;

    private static final String TEST_CORP_CODE = "TEST_CORP";

    @Configuration
    @Import({ SaasPwMybatisConfig.class })
    static class Config {
        @Bean
        PwSysFunctionsDao pwSysFunctionsDao(SqlSessionTemplate sqlSessionTemplate) {
            return new PwSysFunctionsDaoImpl(sqlSessionTemplate);
        }

        @Bean
        DynamicDataSourceMappingConfigPullStorage dynamicDataSourceMappingConfigPullStorage(
                @Value("${nacos.config.server-addr}") String serverAddr,
                @Value("${nacos.config.namespace}") String namespace,
                @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.data-id:saas-pw-datasource-sharding-mapping.json}") String dataId,
                @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.group:DEFAULT_GROUP}") String group,
                @Value("${verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout:3000}") Integer timeout)
                throws NacosException {
            return new DynamicDataSourceMappingConfigPullStorage(serverAddr, namespace, dataId, group, timeout);
        }
    }

    @Before
    public void setUp() {
        // 初始化测试数据 - 插入10条数据
        for (int i = 1; i <= 10; i++) {
            PwSysFunctionsDO function = createTestFunction(i);
            // 由于只实现查询功能，这里需要直接插入SQL
            pwSysFunctionsDao.insert(function);
        }

        // 无 parentId
        for (int i = 11; i <= 20; i++) {
            PwSysFunctionsDO function = createTestFunction(i);
            function.setParentId(null);
            // 由于只实现查询功能，这里需要直接插入SQL
            pwSysFunctionsDao.insert(function);
        }
    }

    @Test
    public void testQueryById() {
        // Given - 插入一条测试数据
        PwSysFunctionsDO function = createTestFunction(100);
        pwSysFunctionsDao.insert(function);
        // When
        PwSysFunctionsDO found = pwSysFunctionsDao.queryById(function.getId());

        // Then
        Assert.assertNotNull(found);
        Assert.assertEquals(function.getFunCode(), found.getFunCode());
        Assert.assertEquals(function.getFunName(), found.getFunName());
        Assert.assertEquals(function.getCorpCode(), found.getCorpCode());
    }

    @Test
    public void testCountByArg() {
        // Given
        PwSysFunctionsQueryArg queryArg = new PwSysFunctionsQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);

        // When
        Integer count = pwSysFunctionsDao.countByArg(queryArg);

        // Then
        Assert.assertNotNull(count);
        Assert.assertTrue(count >= 10); // 至少有setUp中插入的10条数据
    }

    @Test
    public void testQueryByArg() {
        // Given
        PwSysFunctionsQueryArg queryArg = new PwSysFunctionsQueryArg();
        queryArg.setCorpCode(TEST_CORP_CODE);
        queryArg.setOffset(0);
        queryArg.setLimit(5);

        // When
        List<PwSysFunctionsDO> results = pwSysFunctionsDao.queryByArg(queryArg);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.size() <= 5);
    }

    @Test
    public void testQueryBySubSystemId() {
        // Given
        Integer subsystemId = 1;

        // When
        List<PwSysFunctionsDO> results = pwSysFunctionsDao.queryBySubSystemId(subsystemId);

        // Then
        Assert.assertNotNull(results);
        // 验证返回的功能模块的子系统ID都是指定的值
        for (PwSysFunctionsDO function : results) {
            Assert.assertEquals(subsystemId, function.getSubsystemId());
        }
    }

    @Test
    public void testQueryByParentIds() {
        // Given
        List<Long> parentIds = Arrays.asList(1L, 2L);

        // When
        List<PwSysFunctionsDO> results = pwSysFunctionsDao.queryByParentIds(parentIds);

        // Then
        Assert.assertNotNull(results);
        // 验证返回的功能模块的父ID都在指定的列表中
        for (PwSysFunctionsDO function : results) {
            Assert.assertTrue(parentIds.contains(function.getParentId().longValue()));
        }
    }

    @Test
    public void testQueryHasRequisitionList() {
        // 先插入一个已征用的功能模块
        PwSysFunctionsDO requisitionFunction = createTestFunction(999);
        requisitionFunction.setRequisitionFlag(1);
        requisitionFunction.setFunCode("REQUISITION_TEST");
        requisitionFunction.setCorpCode(TEST_CORP_CODE);
        pwSysFunctionsDao.insert(requisitionFunction);

        // When
        List<PwSysFunctionsDO> results = pwSysFunctionsDao.queryHasRequisitionList(TEST_CORP_CODE);

        // Then
        Assert.assertNotNull(results);
        Assert.assertTrue(results.size() >= 1);
        // 验证返回的功能模块都是已征用的
        for (PwSysFunctionsDO function : results) {
            Assert.assertEquals(Integer.valueOf(1), function.getRequisitionFlag());
            Assert.assertEquals(TEST_CORP_CODE, function.getCorpCode());
        }
        // 验证包含我们插入的测试数据
        boolean foundTestData = results.stream()
                .anyMatch(f -> "REQUISITION_TEST".equals(f.getFunCode()));
        Assert.assertTrue("应该包含测试插入的已征用功能模块", foundTestData);
    }

    @Test
    public void testQueryHasRequisitionListWithNullCorpCode() {
        // 先插入一个已征用的功能模块
        PwSysFunctionsDO requisitionFunction = createTestFunction(998);
        requisitionFunction.setRequisitionFlag(1);
        requisitionFunction.setFunCode("REQUISITION_TEST_NULL");
        requisitionFunction.setCorpCode("OTHER_CORP");
        pwSysFunctionsDao.insert(requisitionFunction);

        // When - 传入null的corpCode
        List<PwSysFunctionsDO> results = pwSysFunctionsDao.queryHasRequisitionList(null);

        // Then - 应该返回所有已征用的功能模块，不过滤corpCode
        Assert.assertNotNull(results);
        Assert.assertTrue(results.size() >= 1);
        // 验证返回的功能模块都是已征用的
        for (PwSysFunctionsDO function : results) {
            Assert.assertEquals(Integer.valueOf(1), function.getRequisitionFlag());
        }
    }

    private PwSysFunctionsDO createTestFunction(int index) {
        PwSysFunctionsDO function = new PwSysFunctionsDO();
        function.setCorpCode(TEST_CORP_CODE);
        function.setFunName("Test Function " + index);
        function.setFunCode("TEST_FUN_" + index);
        function.setMenuCode("TEST_MENU_" + index);
        function.setFunIco("icon_" + index + ".png");
        function.setFunUrl("/test/function/" + index);
        function.setFunType("0");
        function.setOptType("test:test");
        function.setParentId(index % 5); // 设置父ID为0-4之间的值
        function.setParentIds("0," + (index % 5));
        function.setSortNo(index);
        function.setHierarchy((short) 1);
        function.setOpenType("_self");
        function.setFunctionType("menu");
        function.setCreateTime(new Date());
        function.setCreateBy("testUser");
        function.setModifyTime(new Date());
        function.setModifyBy("testUser");
        function.setDeleted("F");
        function.setOuterChain("");
        function.setSubsystemId(1);
        function.setCloudFunUrl("/cloud/test/" + index);
        function.setUseFlag("T");
        function.setDescription("Test description " + index);
        function.setIsGroupShow(1);
        function.setRequisitionFlag(0);
        return function;
    }

    @Test
    public void testBatchUpdateAsRequisition() {
        // 准备测试数据：插入3个功能模块
        PwSysFunctionsDO function1 = createTestFunction(1);
        PwSysFunctionsDO function2 = createTestFunction(2);
        PwSysFunctionsDO function3 = createTestFunction(3);

        pwSysFunctionsDao.insert(function1);
        pwSysFunctionsDao.insert(function2);
        pwSysFunctionsDao.insert(function3);

        // 验证初始状态：征用标记都是0
        PwSysFunctionsDO inserted1 = pwSysFunctionsDao.queryById(function1.getId());
        PwSysFunctionsDO inserted2 = pwSysFunctionsDao.queryById(function2.getId());
        PwSysFunctionsDO inserted3 = pwSysFunctionsDao.queryById(function3.getId());

        Assert.assertEquals(Integer.valueOf(0), inserted1.getRequisitionFlag());
        Assert.assertEquals(Integer.valueOf(0), inserted2.getRequisitionFlag());
        Assert.assertEquals(Integer.valueOf(0), inserted3.getRequisitionFlag());

        // 执行批量更新：将function1和function3标记为已征用
        PwSysFunctionsBatchUpdateAsRequisitionArg arg = new PwSysFunctionsBatchUpdateAsRequisitionArg();
        arg.setIds(Arrays.asList(function1.getId(), function3.getId()));
        arg.setModifyBy("testUser");

        int updatedCount = pwSysFunctionsDao.batchUpdateAsRequisition(arg);

        // 验证更新结果
        Assert.assertEquals(2, updatedCount);

        // 验证更新后的状态
        PwSysFunctionsDO updated1 = pwSysFunctionsDao.queryById(function1.getId());
        PwSysFunctionsDO updated2 = pwSysFunctionsDao.queryById(function2.getId());
        PwSysFunctionsDO updated3 = pwSysFunctionsDao.queryById(function3.getId());

        Assert.assertEquals(Integer.valueOf(1), updated1.getRequisitionFlag()); // 已征用
        Assert.assertEquals(Integer.valueOf(0), updated2.getRequisitionFlag()); // 未征用
        Assert.assertEquals(Integer.valueOf(1), updated3.getRequisitionFlag()); // 已征用

        Assert.assertEquals("testUser", updated1.getModifyBy());
        Assert.assertEquals("testUser", updated3.getModifyBy());
    }

    @Test
    public void testBatchUpdateAsRequisitionWithEmptyIds() {
        // 测试空ID列表的情况
        PwSysFunctionsBatchUpdateAsRequisitionArg arg = new PwSysFunctionsBatchUpdateAsRequisitionArg();
        arg.setIds(Arrays.asList());
        arg.setModifyBy("testUser");

        int updatedCount = pwSysFunctionsDao.batchUpdateAsRequisition(arg);

        // 验证没有记录被更新
        Assert.assertEquals(0, updatedCount);
    }
}
