package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2025/6/23 18:03
 */
public interface ExplainerInfoReadService {

    /**
     * 模糊查询讲解人列表
     *
     * @param request 查询条件
     * @return 讲解人列表
     */
    ResultModel<List<ExplainerInfo>> queryExplainerList(ExplainerInfoQueryRequest request);
} 