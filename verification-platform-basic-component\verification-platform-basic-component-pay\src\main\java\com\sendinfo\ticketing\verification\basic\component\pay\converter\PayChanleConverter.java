package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.PayChanleMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.PayChanle;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付渠道转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("payChanleConverter")
public class PayChanleConverter implements
        CreateParam2DoConverter<PayChanleCreateParam, PayChanleDO>,
        ReadDo2ModelConverter<PayChanleDO, PayChanle>,
        ReadParam2ArgConverter<PayChanleQueryParam, PayChanleQueryArg>,
        UpdateParam2ArgConverter<PayChanleUpdateParam, PayChanleUpdateArg, PayChanle>,
        DeleteParam2ArgConverter<PayChanleDeleteParam, PayChanleDeleteArg> {

    private final PayChanleMapper mapper = PayChanleMapper.INSTANCE;

    @Override
    public PayChanleDO c_p2d(PayChanleCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        return mapper.convert(createParam);
    }

    @Override
    public List<PayChanleDO> c_ps2ds(List<PayChanleCreateParam> createParams) {
        if (createParams == null) {
            return null;
        }
        return createParams.stream()
                .map(mapper::convert)
                .collect(Collectors.toList());
    }

    @Override
    public PayChanle r_d2m(PayChanleDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<PayChanle> r_ds2ms(List<PayChanleDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream()
                .map(mapper::convert)
                .collect(Collectors.toList());
    }

    @Override
    public PayChanleQueryArg r_p2a(PayChanleQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        return mapper.convert(queryParam);
    }

    @Override
    public PayChanleUpdateArg u_p2a(PayChanleUpdateParam updateParam, PayChanle currentModel) {
        if (updateParam == null) {
            return null;
        }
        return mapper.convert(updateParam);
    }

    @Override
    public PayChanleDeleteArg d_p2a(PayChanleDeleteParam deleteParam) {
        if (deleteParam == null) {
            return null;
        }
        return mapper.convert(deleteParam);
    }
} 