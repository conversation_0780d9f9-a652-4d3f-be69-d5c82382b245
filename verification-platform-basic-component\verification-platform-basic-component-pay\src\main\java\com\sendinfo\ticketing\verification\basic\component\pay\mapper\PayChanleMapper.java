package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.PayChanleUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.PayChanle;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.PayChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.PayChanleDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 支付渠道对象映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
    imports = {CommonUseFlagEnum.class}
)
public interface PayChanleMapper {
    PayChanleMapper INSTANCE = Mappers.getMapper(PayChanleMapper.class);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    @Mapping(target = "useFlag", expression = "java(createParam.getUseFlag() != null ? createParam.getUseFlag().getCode() : null)")
    PayChanleDO convert(PayChanleCreateParam createParam);

    /**
     * 数据对象转换为模型
     *
     * @param dataObject 数据对象
     * @return 模型
     */
    @Mapping(target = "useFlag", expression = "java(CommonUseFlagEnum.ofCode(dataObject.getUseFlag()))")
    PayChanle convert(PayChanleDO dataObject);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    @Mapping(target = "useFlag", expression = "java(queryParam.getUseFlag() != null ? queryParam.getUseFlag().getCode() : null)")
    PayChanleQueryArg convert(PayChanleQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     *
     * @param updateParam 更新参数
     * @return 更新参数
     */
    @Mapping(target = "useFlag", expression = "java(updateParam.getUseFlag() != null ? updateParam.getUseFlag().getCode() : null)")
    PayChanleUpdateArg convert(PayChanleUpdateParam updateParam);

    /**
     * 删除参数转换为删除参数
     *
     * @param deleteParam 删除参数
     * @return 删除参数
     */
    PayChanleDeleteArg convert(PayChanleDeleteParam deleteParam);
} 