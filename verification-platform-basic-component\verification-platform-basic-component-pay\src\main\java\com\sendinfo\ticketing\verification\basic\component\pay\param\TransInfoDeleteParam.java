package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractDeleteParam;
import lombok.Data;
import lombok.ToString;

/**
 * 交易记录删除参数
 * @since 1.0.0
 */
@Data
@ToString(callSuper = true)
public class TransInfoDeleteParam extends AbstractDeleteParam {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 修改人
     */
    private String modifyBy;
} 