package com.sendinfo.ticketing.verification.basic.api.customer;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelAssemblyCreateRequest;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 旅行社信息创建服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22 16:56
 */
public interface TravelInfoCreateService {

	/**
	 * 创建旅行社信息
	 *
	 * @param travelAssemblyCreateRequest
	 * @return
	 */
	ResultModel<TravelInfo> createTravelInfo(TravelAssemblyCreateRequest travelAssemblyCreateRequest);
}
