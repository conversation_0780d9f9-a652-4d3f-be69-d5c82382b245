package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.SysPaySetReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.SysPaySetConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysPaySetDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * 支付标签读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysPaySetReadComponent")
@Getter
public class SysPaySetReadComponentImpl implements
        SysPaySetReadComponent,
        DaoBasedSingleRead<Long, SysPaySetQueryParam, SysPaySet, SysPaySetDO>,
        DaoBasedListRead<Long, SysPaySetQueryParam, SysPaySet, SysPaySetDO, SysPaySetQueryArg>,
        DaoBasedCountRead<Long, SysPaySetQueryParam, SysPaySet, SysPaySetQueryArg> {

    private final SysPaySetDao dao;
    private final SysPaySetConverter converter;

    public SysPaySetReadComponentImpl(SysPaySetDao dao, SysPaySetConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<SysPaySet> queryEnableListByPayTypeAndId(String payType, Set<Long> idSet, String corpCode) {
        return converter.r_ds2ms(dao.queryEnableListByPayTypeAndId(payType, idSet, corpCode));
    }
}