package com.sendinfo.ticketing.verification.basic.api.park;

import com.sendinfo.ticketing.verification.basic.api.park.request.TktParkZoneQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.park.TktParkZone;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotNull;

public interface TktParkZoneReadService {
    /**
     * 分页查询景区列表
     * @param pageRequest
     * @return
     */
    PageResultModel<TktParkZone> searchParkZone(PageRequest<TktParkZoneQueryCondition> pageRequest);
    /**
     * 根据景区编码和租户编码查询景区信息
     * @param parkCode
     * @param corpCode
     * @return
     */
    ResultModel<TktParkZone> queryParkZoneByParkCodeAndCorpCode(@NotNull String parkCode, @NotNull String corpCode);

    /**
     * 根据id查询景区信息
     * @param id
     * @return
     */
    ResultModel<TktParkZone> queryParkZoneById(@NotNull Long id,@NotNull String corpCode);
}
