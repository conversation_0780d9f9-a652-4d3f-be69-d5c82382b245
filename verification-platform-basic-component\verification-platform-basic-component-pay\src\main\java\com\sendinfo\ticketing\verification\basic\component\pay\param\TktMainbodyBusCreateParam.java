package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.model.pay.enums.BusTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 收入主体业务设置创建参数
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktMainbodyBusCreateParam extends AbstractCreateParam {
    /**
     * primary key
     */
    private Long id;
    /**
     * create by
     */
    private String createBy;
    /**
     * modified by
     */
    private String modifyBy;
    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 业务ID
     */
    private Long busId;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 主体名称
     */
    private String mainbodyName;

    /**
     * 业务类型
     */
    private BusTypeEnum busType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 组标识
     */
    private String groupFlag;
} 