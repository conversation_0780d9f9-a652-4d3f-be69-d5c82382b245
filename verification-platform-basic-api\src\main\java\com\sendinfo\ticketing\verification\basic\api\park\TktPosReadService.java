package com.sendinfo.ticketing.verification.basic.api.park;

import com.sendinfo.ticketing.verification.basic.api.park.request.TktPosQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.park.request.TktPosQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.park.TktPos;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 售票窗口读取服务
 * <AUTHOR>
 */
public interface TktPosReadService {

    /**
     * 分页查询售票窗口列表
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<TktPos> query(PageRequest<TktPosQueryCondition> pageRequest);

    /**
     * 根据窗口MAC查询售票窗口
     *
     * @param request
     * @return
     */
    ResultModel<TktPos> getTktPosByPosMac(TktPosQueryRequest request);

} 