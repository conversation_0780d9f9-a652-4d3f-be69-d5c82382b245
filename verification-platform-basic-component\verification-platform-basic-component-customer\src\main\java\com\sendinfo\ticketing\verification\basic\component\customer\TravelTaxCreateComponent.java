package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxCreateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelTax;
import com.sendinfo.ticketing.verification.common.component.CreateComponent;

/**
 * travel_tax信息创建组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TravelTaxCreateComponent extends CreateComponent<TravelTaxCreateParam, Long> {

    /**
     * 创建travel_tax信息
     *
     * @param createParam 旅行社发票集合
     * @return travel_tax信息
     */
    TravelTax createTravelTax(TravelTaxCreateParam createParam);
} 