package com.sendinfo.ticketing.verification.basic.api.customer.request;

import com.sendinfo.ticketing.verification.basic.api.account.request.CapitalAccountCreateRequest;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 旅行社信息创建请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17 19:33
 */
@Getter
@Setter
@ToString
public class TravelAssemblyCreateRequest implements Serializable {

	/**
	 * 旅行社信息创建请求对象
	 */
	private TravelInfoCreateRequest travelInfoCreateRequest;
	/**
	 * 旅行社税信息创建请求对象
	 */
	private TravelTaxCreateRequest travelTaxCreateRequest;
	/**
	 * 旅行社资金账户创建请求对象
	 */
	private CapitalAccountCreateRequest capitalAccountCreateRequest;
}
