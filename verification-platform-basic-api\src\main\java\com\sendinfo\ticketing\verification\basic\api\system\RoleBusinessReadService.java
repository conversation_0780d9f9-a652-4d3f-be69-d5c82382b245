package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.RoleBusinessQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.system.request.RoleBusinessRoleIdsQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.RoleBusiness;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 角色业务权限读取服务接口
 * 对应表：role_business
 * 方法、注释、参数、返回值类型严格对齐 system/api 层
 * <AUTHOR>
 */
public interface RoleBusinessReadService {

    /**
     * 根据主键ID查询角色业务权限
     *
     * @param id 主键ID
     * @return 角色业务权限信息
     */
    ResultModel<RoleBusiness> queryById(@NotNull Long id);

    /**
     * 根据条件查询角色业务权限列表
     *
     * @param request 查询条件
     * @return 角色业务权限列表
     */
    ResultModel<List<RoleBusiness>> queryList(RoleBusinessQueryRequest request);

    /**
     * 根据条件查询角色业务权限列表
     *
     * @param queryRequest 通过业务角色ID查询角色业务
     * @return 角色业务权限列表
     */
    ResultModel<List<RoleBusiness>> queryByRoleIds(RoleBusinessRoleIdsQueryRequest queryRequest);
} 