package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractQueryArg;
import com.sendinfo.ticketing.verification.common.repository.arg.Pageable;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置查询参数
 * 用于封装sys_mainbody_manage表的查询条件，支持租户隔离、分页、排序
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageQueryArg extends AbstractQueryArg implements Pageable {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 经营主体ID
     */
    private Long mainbodyId;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 经营主体名称
     */
    private String mainbodyName;

    /**
     * 经营主体简称
     */
    private String mainbodyShortName;

    /**
     * 支付渠道名称
     */
    private String payChannelName;

    /**
     * 支付渠道编号
     */
    private String payChannelCode;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private String useFlag;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkTel;

    /**
     * 经营主体编号
     */
    private String mainbodyNumber;

    /**
     * 分页偏移量
     */
    private Integer offset;

    /**
     * 分页大小
     */
    private Integer limit;
} 