package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.TransInfoCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TransInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TransInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 交易记录创建组件实现
 *
 * @since 1.0.0
 */
@Component("transInfoCreateComponent")
@Getter
public class TransInfoCreateComponentImpl implements
        TransInfoCreateComponent,
        DaoBasedSingleCreate<Long, TransInfoCreateParam, TransInfoDO> {

    private final TransInfoDao dao;
    private final TransInfoConverter converter;

    public TransInfoCreateComponentImpl(TransInfoDao dao, TransInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 