package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.AuditStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.GroupCoSyncEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.LangTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.TerminalTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 导游查询参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class GuideQueryParam extends AbstractPageQueryParam {

    /**
     * 姓名
     */
    private String guiderName;

    /**
     * 导游证
     */
    private String guideNo;

    /**
     * 身份证号
     */
    private String idcardNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 语种类型
     */
    private LangTypeEnum langType;

    /**
     * 性别
     */
    private String sex;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 导游级别
     */
    private String gudierLevelCode;

    /**
     * 审核状态
     */
    private AuditStatusEnum auditStatus;

    /**
     * 启用状态
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 旅行社ID
     */
    private Long travelId;

    /**
     * 外导类型取系统参数
     */
    private String extemalType;

    /**
     * 导游编号
     */
    private String guideNum;

    /**
     * 终端类型
     */
    private TerminalTypeEnum terminalType;

    /**
     * 拼音码
     */
    private String pingCode;

    /**
     * 集团同步状态
     */
    private GroupCoSyncEnum isGroupCoSync;

    /**
     * 证件类型
     */
    private String idCardType;

    /**
     * 企业编码
     */
    private String corpCode;
} 