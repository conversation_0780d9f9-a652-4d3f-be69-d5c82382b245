package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.AuditStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.GroupCoSyncEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.LangTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.customer.enums.TerminalTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 导游创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class GuideCreateParam extends AbstractCreateParam {

    /**
     * 姓名
     */
    private String guiderName;

    /**
     * 导游证
     */
    private String guideNo;

    /**
     * 导游证照片
     */
    private String guideImg;

    /**
     * 身份证号
     */
    private String idcardNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 语种类型: 0-汉语 1-保留 2-英语 3-韩语 4-法语 5-日语 6-德语 9-其他
     */
    private LangTypeEnum langType;

    /**
     * 性别
     */
    private String sex;

    /**
     * 公司名称
     */
    private String corpName;

    /**
     * 导游级别
     */
    private String gudierLevelCode;

    /**
     * 导游级别名称
     */
    private String gudierLevelName;

    /**
     * 地址
     */
    private String address;

    /**
     * 审核状态 0:未审核  1:已审核
     */
    private AuditStatusEnum auditStatus;

    /**
     * 启禁用   T：启用   F：禁用
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 旅行社ID
     */
    private Long travelId;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 外导类型取系统参数
     */
    private String extemalType;

    /**
     * 导游编号
     */
    private String guideNum;

    /**
     * 端类型 1：pc端 2：移动端
     */
    private TerminalTypeEnum terminalType;

    /**
     * 迁移数据
     */
    private String transferParam;

    /**
     * 拼音码
     */
    private String pingCode;

    /**
     * 是否集团同步 0租户 1集团
     */
    private GroupCoSyncEnum isGroupCoSync;

    /**
     * 证件类型
     */
    private String idCardType;

    /**
     * 最近修改人：真实姓名（用户名）
     */
    private String recentlyUpdater;
} 