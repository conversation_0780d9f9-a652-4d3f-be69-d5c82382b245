<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.sendinfo.ticketing</groupId>
        <artifactId>verification-platform-basic</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <packaging>jar</packaging>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <artifactId>verification-platform-basic-bootstrap</artifactId>
    <name>verification-platform-basic-bootstrap</name>
    <dependencies>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-park</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-auth</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-customer</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-goods</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-platform-basic-service-infra</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.paas</groupId>
            <artifactId>sendinfo-paas-cache-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.paas</groupId>
            <artifactId>sendinfo-paas-rds-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.paas</groupId>
            <artifactId>sendinfo-paas-message-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sendinfo.ticketing</groupId>
            <artifactId>verification-common-flow-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <finalName>verification-platform-basic</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <configuration>
                    <outputDirectory>../target</outputDirectory>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
