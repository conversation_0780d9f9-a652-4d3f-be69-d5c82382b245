package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktRefundRuleQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.goods.request.TktRefundRuleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.TktRefundRule;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;
import java.util.Set;

/**
 * 退单规则读取服务
 *
 * <AUTHOR>
 */
public interface TktRefundRuleReadService {

    /**
     * 分页查询票型退单规则
     *
     * @param condition 分页查询条件
     * @return 分页结果
     */
    PageResultModel<TktRefundRule> query(PageRequest<TktRefundRuleQueryCondition> condition);

    /**
     * 根据票ID查询退单规则
     *
     * @param request
     * @return
     */
    ResultModel<TktRefundRule> getTktRefundRuleByTicketId(TktRefundRuleQueryRequest request);

    /**
     * 根据票型ID集合获取退单规则
     *
     * @param ticketIdSet 票型ID集合
     * @param corpCode    企业编码
     * @return 退票规则列表
     */
    ResultModel<List<TktRefundRule>> getTktRefundRuleByTicketIds(Set<Long> ticketIdSet, String corpCode);
} 