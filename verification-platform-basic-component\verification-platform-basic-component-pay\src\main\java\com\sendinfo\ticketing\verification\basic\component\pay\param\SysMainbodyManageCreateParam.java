package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置创建参数
 * 用于封装创建经营主体支付配置所需的参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageCreateParam extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 经营主体名称
     */
    private String mainbodyName;

    /**
     * 经营主体简称
     */
    private String mainbodyShortName;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 支付应用ID(支付中心分配)
     */
    private String merchantSourceNo;

    /**
     * 支付渠道名称
     */
    private String payChannelName;

    /**
     * 支付渠道编号
     */
    private String payChannelCode;

    /**
     * 商户公钥
     */
    private String paycenterPublicKey;

    /**
     * 商户私钥
     */
    private String mchPrivateKey;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 联系人
     */
    private String linkMan;

    /**
     * 联系电话
     */
    private String linkTel;

    /**
     * logo
     */
    private String logo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 经营主体ID
     */
    private Long mainbodyId;

    /**
     * 经营主体编号
     */
    private String mainbodyNumber;

    /**
     * 结算主体商户号
     */
    private String seettlementMerchantNo;

    /**
     * create by
     */
    private String createBy;

    /**
     * modify by
     */
    private String modifyBy;
} 