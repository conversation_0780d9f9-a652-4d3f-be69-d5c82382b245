package com.sendinfo.ticketing.verification.basic.component.customer.impl;

import com.sendinfo.ticketing.verification.basic.component.customer.TravelInfoCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.customer.converter.TravelInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.dao.TravelInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 导游读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("travelInfoCreateComponent")
@Getter
public class TravelInfoCreateComponentImpl implements TravelInfoCreateComponent,
        DaoBasedSingleCreate<Long, TravelInfoCreateParam, TravelInfoDO> {

    private final TravelInfoDao dao;
    private final TravelInfoConverter converter;

    public TravelInfoCreateComponentImpl(TravelInfoDao dao, TravelInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public TravelInfo createTravelInfo(TravelInfoCreateParam createParam) {
        TravelInfoDO travelInfoDO = converter.c_p2d(createParam);
        dao.insert(travelInfoDO);

        return converter.r_d2m(travelInfoDO);
    }
}