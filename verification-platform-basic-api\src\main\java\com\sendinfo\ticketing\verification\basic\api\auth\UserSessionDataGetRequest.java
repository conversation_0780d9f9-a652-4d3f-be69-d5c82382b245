package com.sendinfo.ticketing.verification.basic.api.auth;

import com.sendinfo.ticketing.verification.basic.model.auth.enums.JwtTokenSource;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-07-27 15:50:33
 */
@Getter
@Setter
@ToString
public class UserSessionDataGetRequest implements Serializable {

    private static final long serialVersionUID = 2314117790604661987L;

    @NotNull
    private Map<String, String> headers;

    @NotNull
    private JwtTokenSource jwtTokenSource;
}
