package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.TktPrintQueryCondition;
import com.sendinfo.ticketing.verification.basic.api.goods.request.TktPrintQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.TktPrint;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 打印设置读取服务
 * <AUTHOR>
 */
public interface TktPrintReadService {

    /**
     * 分页查询打印设置
     *
     * @param condition 分页查询条件
     * @return 分页结果
     */
    PageResultModel<TktPrint> query(PageRequest<TktPrintQueryCondition> condition);

    /**
     * 根据票码查询打印设置
     *
     * @param request
     * @return  TktPrint
     */
    ResultModel<TktPrint> getTktPrintByTicketCode(TktPrintQueryRequest request);
    /**
     * 批量查询打印设置
     *
     * @param ticketIds
     * @return
     */
    ResultModel<List<TktPrint>> batchQueryTktPrintByTicketIds(@NotEmpty List<Long> ticketIds,@NotNull String corpCode);
} 