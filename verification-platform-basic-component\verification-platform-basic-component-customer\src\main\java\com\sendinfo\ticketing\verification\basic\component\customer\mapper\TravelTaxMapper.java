package com.sendinfo.ticketing.verification.basic.component.customer.mapper;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelTaxCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelTaxQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelTaxQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelTax;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelTaxQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelTaxDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * travel_tax对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface TravelTaxMapper {

    TravelTaxMapper INSTANCE = Mappers.getMapper(TravelTaxMapper.class);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    TravelTaxQueryArg convert(TravelTaxQueryParam queryParam);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryRequest 查询参数
     * @return 查询参数
     */
    TravelTaxQueryParam convert(TravelTaxQueryRequest queryRequest);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    TravelTax convert(TravelTaxDO dataObject);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam   创建参数
     * @return  TravelTaxDO
     */
    TravelTaxDO convert(TravelTaxCreateParam createParam);

    /**
     * 创建参数转换为数据对象
     * @param createRequest 创建对象
     * @return  TravelTaxCreateParam
     */
    TravelTaxCreateParam convert(TravelTaxCreateRequest createRequest);
} 