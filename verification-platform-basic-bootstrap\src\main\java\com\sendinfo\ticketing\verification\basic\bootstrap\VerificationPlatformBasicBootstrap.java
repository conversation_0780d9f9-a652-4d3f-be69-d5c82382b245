package com.sendinfo.ticketing.verification.basic.bootstrap;

import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * <AUTHOR> 2025-05-15 10:34:20
 */
@SpringBootApplication(scanBasePackages = {"com.sendinfo.ticketing.verification.basic" })
@NacosPropertySource(dataId = "verification-platform-basic.properties", autoRefreshed = true, first = true)
@EnableConfigurationProperties
public class VerificationPlatformBasicBootstrap {

    public static void main(String[] args) {
        SpringApplication.run(VerificationPlatformBasicBootstrap.class, args);
    }

    @Bean
    MeterRegistryCustomizer<MeterRegistry> configurer(@Value("${spring.application.name}") String applicationName){
        return registry -> registry.config().commonTags("application", applicationName);
    }
}
