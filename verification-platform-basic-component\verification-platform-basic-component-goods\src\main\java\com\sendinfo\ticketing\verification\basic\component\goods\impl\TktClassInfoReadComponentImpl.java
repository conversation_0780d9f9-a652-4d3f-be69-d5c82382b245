package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktClassInfoReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktClassInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktClassInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktClassInfo;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktClassInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktClassInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktClassInfoDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/12 17:58
 */
@Getter
@Component("tktClassInfoReadComponent")
public class TktClassInfoReadComponentImpl implements TktClassInfoReadComponent,
        DaoBasedSingleRead<Long, TktClassInfoQueryParam, TktClassInfo, TktClassInfoDO>,
        DaoBasedCountRead<Long, TktClassInfoQueryParam, TktClassInfo, TktClassInfoQueryArg>,
        DaoBasedListRead<Long, TktClassInfoQueryParam, TktClassInfo, TktClassInfoDO, TktClassInfoQueryArg> {

    private final TktClassInfoDao dao;

    private final TktClassInfoConverter converter;


    public TktClassInfoReadComponentImpl(TktClassInfoDao dao, TktClassInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TktClassInfo> batchQueryClassInfoByClassCodes(List<String> classCodes, String corpCode) {
        List<TktClassInfoDO> tktClassInfoDOS = dao.batchQueryClassInfoByClassCodes(classCodes, corpCode);
        return Optional.ofNullable(tktClassInfoDOS)
                .map(converter::r_ds2ms)
                .orElse(Collections.emptyList());
    }
}
