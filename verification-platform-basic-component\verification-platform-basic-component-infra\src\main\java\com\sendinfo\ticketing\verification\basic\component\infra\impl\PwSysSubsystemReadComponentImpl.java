package com.sendinfo.ticketing.verification.basic.component.infra.impl;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.sendinfo.ticketing.verification.basic.component.infra.PwSysSubsystemReadComponent;
import com.sendinfo.ticketing.verification.basic.component.infra.converter.PwSysSubsystemConverter;
import com.sendinfo.ticketing.verification.basic.model.infra.PwSysSubsystem;
import com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwSysSubsystemDao;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysSubsystemDO;

/**
 * 子系统管理查询组件实现类，负责查询子系统的业务逻辑
 *
 * <AUTHOR> 2025-07-24 15:40:00
 */
@Component("pwSysSubsystemReadComponent")
@Getter
public class PwSysSubsystemReadComponentImpl implements PwSysSubsystemReadComponent {

    private final PwSysSubsystemDao dao;
    private final PwSysSubsystemConverter converter;

    public PwSysSubsystemReadComponentImpl(PwSysSubsystemDao dao, PwSysSubsystemConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<PwSysSubsystem> queryAllInUseList(String corpCode) {
        List<PwSysSubsystemDO> dataObjects = dao.queryAllInUseList(corpCode);
        if (dataObjects == null) {
            return Collections.emptyList();
        }
        return dataObjects.stream()
                .map(converter::r_d2m)
                .collect(Collectors.toList());
    }

}