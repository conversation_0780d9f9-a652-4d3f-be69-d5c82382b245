package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.SysMainbodyManageSubMerchantsReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.SysMainbodyManageSubMerchantsConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyManageSubMerchantsDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 经营主体子商户读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyManageSubMerchantsReadComponent")
@Getter
public class SysMainbodyManageSubMerchantsReadComponentImpl implements
        SysMainbodyManageSubMerchantsReadComponent,
        DaoBasedSingleRead<Long, SysMainbodyManageSubMerchantsQueryParam, SysMainbodyManageSubMerchants, SysMainbodyManageSubMerchantsDO>,
        DaoBasedListRead<Long, SysMainbodyManageSubMerchantsQueryParam, SysMainbodyManageSubMerchants, SysMainbodyManageSubMerchantsDO, SysMainbodyManageSubMerchantsQueryArg>,
        DaoBasedCountRead<Long, SysMainbodyManageSubMerchantsQueryParam, SysMainbodyManageSubMerchants, SysMainbodyManageSubMerchantsQueryArg> {

    private final SysMainbodyManageSubMerchantsDao dao;
    private final SysMainbodyManageSubMerchantsConverter converter;

    public SysMainbodyManageSubMerchantsReadComponentImpl(SysMainbodyManageSubMerchantsDao dao, SysMainbodyManageSubMerchantsConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 