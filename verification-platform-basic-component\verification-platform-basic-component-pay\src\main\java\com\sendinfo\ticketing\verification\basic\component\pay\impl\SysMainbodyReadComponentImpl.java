package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.SysMainbodyReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.SysMainbodyConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbody;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.SysMainbodyDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 经营主体读取组件实现
 * 负责经营主体的读取业务逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyReadComponent")
@Getter
public class SysMainbodyReadComponentImpl implements
        SysMainbodyReadComponent,
        DaoBasedSingleRead<Long, SysMainbodyQueryParam, SysMainbody, SysMainbodyDO>,
        DaoBasedListRead<Long, SysMainbodyQueryParam, SysMainbody, SysMainbodyDO, SysMainbodyQueryArg>,
        DaoBasedCountRead<Long, SysMainbodyQueryParam, SysMainbody, SysMainbodyQueryArg> {

    private final SysMainbodyDao dao;
    private final SysMainbodyConverter converter;

    public SysMainbodyReadComponentImpl(SysMainbodyDao dao, SysMainbodyConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 