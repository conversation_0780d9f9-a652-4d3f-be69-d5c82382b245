package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体支付配置更新参数
 * 用于封装更新经营主体支付配置所需的参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 主体商户号
     */
    private String merchantNo;

    /**
     * 支付应用ID(支付中心分配)
     */
    private String merchantSourceNo;

    /**
     * 支付渠道名称
     */
    private String payChannelName;

    /**
     * 支付渠道编号
     */
    private String payChannelCode;

    /**
     * 商户公钥
     */
    private String paycenterPublicKey;

    /**
     * 商户私钥
     */
    private String mchPrivateKey;

    /**
     * 启用状态：T:启用（默认）,F:不启用
     */
    private CommonUseFlagEnum useFlag;

    /**
     * logo
     */
    private String logo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 结算主体商户号
     */
    private String seettlementMerchantNo;

    /**
     * 修改人
     */
    private String modifyBy;
} 