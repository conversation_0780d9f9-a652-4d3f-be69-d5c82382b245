package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetUpdateParam;
import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayDefaultEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayTypeEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * 支付标签对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
    imports = {PayTypeEnum.class, CommonUseFlagEnum.class, PayDefaultEnum.class},
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface SysPaySetMapper {

    SysPaySetMapper INSTANCE = Mappers.getMapper(SysPaySetMapper.class);

    // Param -> DO
    @Mapping(target = "payType", expression = "java(createParam.getPayType() != null ? createParam.getPayType().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(createParam.getUseFlag() != null ? createParam.getUseFlag().getCode() : null)")
    @Mapping(target = "payDefault", expression = "java(createParam.getPayDefault() != null ? createParam.getPayDefault().getCode() : null)")
    SysPaySetDO convert(SysPaySetCreateParam createParam);

    // Param -> Repository Arg
    @Mapping(target = "payType", expression = "java(queryParam.getPayType() != null ? queryParam.getPayType().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(queryParam.getUseFlag() != null ? queryParam.getUseFlag().getCode() : null)")
    @Mapping(target = "payDefault", expression = "java(queryParam.getPayDefault() != null ? queryParam.getPayDefault().getCode() : null)")
    SysPaySetQueryArg convert(SysPaySetQueryParam queryParam);

    // UpdateParam -> Repository Update Arg
    @Mapping(target = "payType", expression = "java(updateParam.getPayType() != null ? updateParam.getPayType().getCode() : null)")
    @Mapping(target = "useFlag", expression = "java(updateParam.getUseFlag() != null ? updateParam.getUseFlag().getCode() : null)")
    @Mapping(target = "payDefault", expression = "java(updateParam.getPayDefault() != null ? updateParam.getPayDefault().getCode() : null)")
    SysPaySetUpdateArg convert(SysPaySetUpdateParam updateParam);

    // DO -> Model
    @Mapping(target = "payType", expression = "java(PayTypeEnum.ofCode(dataObject.getPayType()))")
    @Mapping(target = "useFlag", expression = "java(CommonUseFlagEnum.ofCode(dataObject.getUseFlag()))")
    @Mapping(target = "payDefault", expression = "java(PayDefaultEnum.ofCode(dataObject.getPayDefault()))")
    SysPaySet convert(SysPaySetDO dataObject);
} 