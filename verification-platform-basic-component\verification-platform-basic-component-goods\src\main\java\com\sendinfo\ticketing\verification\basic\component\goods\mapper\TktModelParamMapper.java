package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktModelParamQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktModelParam;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TktModelParamMapper {
    TktModelParamMapper INSTANCE = Mappers.getMapper(TktModelParamMapper.class);

    TktModelParam convert(TktModelParamDO tktModelParamDO);

    TktModelParamQueryArg convert(TktModelParamQueryParam tktModelParamQueryParam);
}
