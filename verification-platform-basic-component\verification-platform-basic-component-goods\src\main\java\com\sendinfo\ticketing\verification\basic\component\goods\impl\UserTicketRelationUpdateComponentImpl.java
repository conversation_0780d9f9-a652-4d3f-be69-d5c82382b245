package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sendinfo.paas.cache.client.api.Cache;
import com.sendinfo.paas.cache.client.api.CacheResult;
import com.sendinfo.ticketing.verification.basic.api.context.TicketSyncContext;
import com.sendinfo.ticketing.verification.basic.common.util.DataTypeUtil;
import com.sendinfo.ticketing.verification.basic.component.goods.UserTicketRelationUpdateComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.UserTicketRelationConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationUpdateParam;
import com.sendinfo.ticketing.verification.basic.constants.OrganizationSystemCodes;
import com.sendinfo.ticketing.verification.basic.model.goods.UserTicketRelation;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.UserTicketRelationDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.UserTicketRelationDO;
import com.sendinfo.ticketing.verification.basic.repository.system.dao.SysRolePlugsDao;
import com.sendinfo.ticketing.verification.basic.repository.system.dao.UserInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.system.dao.UserRoleDao;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.SysRolePlugsDO;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.UserInfoDO;
import com.sendinfo.ticketing.verification.basic.repository.system.dataobject.UserRoleDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-07-24 10:45
 */
@Slf4j
@Getter
@Component("userTicketRelationUpdateComponent")
public class UserTicketRelationUpdateComponentImpl implements UserTicketRelationUpdateComponent,
        DaoBasedSingleUpdate<UserTicketRelationUpdateParam, UserTicketRelationUpdateArg, UserTicketRelation> {

    private final Cache cache;
    private final UserTicketRelationDao dao;
    private final UserTicketRelationConverter converter;
    private final UserInfoDao userInfoDao;
    private final UserRoleDao userRoleDao;
    private final SysRolePlugsDao sysRolePlugsDao;

    public UserTicketRelationUpdateComponentImpl(@Qualifier("defaultCache") Cache cache
            , UserTicketRelationDao dao
            , UserTicketRelationConverter converter
            , UserInfoDao userInfoDao
            , UserRoleDao userRoleDao
            , SysRolePlugsDao sysRolePlugsDao) {
        this.cache = cache;
        this.dao = dao;
        this.converter = converter;
        this.userInfoDao = userInfoDao;
        this.userRoleDao = userRoleDao;
        this.sysRolePlugsDao = sysRolePlugsDao;
    }

    @Override
    public void refreshRelation(TicketSyncContext context) {
        log.debug("【UserTicketRelationUpdateComponent】 refreshRelation start");
        if(context.getOperateTime() == null){
            context.setOperateTime(new Date());
        }
        List<Long> beforeUserIdList = Lists.newArrayList();
        Long userId = context.getUserId();
        Date operateTime = context.getOperateTime();

        if (Objects.isNull(userId)) {
            Long sysRoleId = context.getSysRoleId();
            List<UserRoleDO> userRoleDOS = userRoleDao.getListBySysRoleId(sysRoleId);

            if (!CollectionUtils.isEmpty(userRoleDOS)) {
                beforeUserIdList = userRoleDOS.stream()
                        .map(UserRoleDO::getUserId)
                        .distinct()
                        .collect(Collectors.toList());
            }
        } else {
            beforeUserIdList.add(userId);
        }

        if (CollectionUtils.isEmpty(beforeUserIdList)) {
            // 没有获取到用户信息,结束
            return;
        }

        List<Long> userIdList = Lists.newArrayList();
        for (Long thisUserId : beforeUserIdList) {
            String cacheKey = OrganizationSystemCodes.USER_TICKET_RELATION_CACHE_KEY + thisUserId;
            CacheResult<byte[]> cacheResult = cache.sync().get(cacheKey);
            if (Objects.nonNull(cacheResult.getData())) {
                long timeStramp = DataTypeUtil.byteToLong(cacheResult.getData());
                Date lastUpdateTime = new Date(timeStramp);
                log.debug("【UserTicketRelationUpdateComponent】 this userId {} last update time is {}", thisUserId, lastUpdateTime);
                // 如果操作时间比上次更新时间早,则过滤不吹
                if (operateTime.compareTo(lastUpdateTime) < 0) {
                    continue;
                }
            }
            userIdList.add(thisUserId);
        }

        if (CollectionUtils.isEmpty(userIdList)) {
            log.debug("【UserTicketRelationUpdateComponent】 this time doesn't have user need refresh.");
            return;
        }

        //为每个用户重置人票关系;
        //1.获取所有用户信息,为了得到corpCode;
        List<UserInfoDO> userInfoDOS = userInfoDao.queryByIds(userIdList);
        //2.通过用户ID获取所有用户与角色绑定关系数据,得到所有关联的角色ID;
        List<UserRoleDO> userRoleDOS = userRoleDao.selectByUserIds(userIdList);
        List<Long> sysRoleIds = userRoleDOS.stream()
                .map(UserRoleDO::getSysRoleId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sysRoleIds)) {
            sysRoleIds.add(0L);
        }

        //3.通过角色ID信息获取所有角色下绑定的票型ID
        List<SysRolePlugsDO> sysRolePlugsDOS = sysRolePlugsDao.getTicketListBySysRoleIds(sysRoleIds);

        // 用户Map化
        Map<Long, UserInfoDO> userInfoMap = userInfoDOS.stream()
                .collect(Collectors.toMap(UserInfoDO::getId, item -> item));

        // 用户绑定角色关系按用户ID分组
        Map<Long, List<UserRoleDO>> userRolesMap = userRoleDOS.stream()
                .collect(Collectors.groupingBy(UserRoleDO::getUserId));

        // 角色绑定票型关系按角色ID分组
        Map<Long, List<SysRolePlugsDO>> sysRolePlugsMap = sysRolePlugsDOS.stream()
                .collect(Collectors.groupingBy(SysRolePlugsDO::getSysRoleId));


        for (Long thisUserId : userIdList) {
            UserInfoDO userInfoDO = userInfoMap.get(thisUserId);
            if (Objects.isNull(userInfoDO)) {
                log.debug("【UserTicketRelationUpdateComponent】 userinfo is null : {}", thisUserId);
                continue;
            }
            List<UserRoleDO> userRoleDOList = userRolesMap.get(thisUserId);
            if (Objects.isNull(userRoleDOList)) {
                log.debug("【UserTicketRelationUpdateComponent】 user role bind info error : {}", thisUserId);
                continue;
            }
            List<Long> sysRoleIdList = userRoleDOList.stream()
                    .map(UserRoleDO::getSysRoleId)
                    .distinct()
                    .collect(Collectors.toList());

            Set<Long> ticketIdList = Sets.newConcurrentHashSet();

            for (Long sysRoleId : sysRoleIdList) {
                List<SysRolePlugsDO> sysRolePlugsDOList = sysRolePlugsMap.get(sysRoleId);
                if (Objects.isNull(sysRolePlugsDOList)) {
                    log.debug("【UserTicketRelationUpdateComponent】 role ticket bind info error : {}", sysRoleId);
                    continue;
                }
                sysRolePlugsDOList.forEach(inner -> ticketIdList.add(inner.getSysFunctionsId()));
            }
            if (CollectionUtils.isEmpty(ticketIdList)) {
                log.debug("【UserTicketRelationUpdateComponent】 user ticket bind info errer : {}", thisUserId);
                continue;
            }
            refreshData(userInfoDO, ticketIdList, operateTime);
        }

    }

    /**
     * 重置数据库里的人票关系数据
     *
     * @param userInfoDO   用户信息
     * @param ticketIdList 关联的票型ID
     * @param operateTime  操作时间
     */
    private void refreshData(UserInfoDO userInfoDO, Set<Long> ticketIdList, Date operateTime) {
        if( operateTime == null){
            operateTime = new Date();
        }
        //组装数据
        List<UserTicketRelationDO> list = Lists.newArrayList();
        for (Long ticketId : ticketIdList) {
            UserTicketRelationDO userTicketRelationDO = new UserTicketRelationDO();
            userTicketRelationDO.setCorpCode(userInfoDO.getCorpCode());
            userTicketRelationDO.setUserId(userInfoDO.getId());
            userTicketRelationDO.setUserAccName(userInfoDO.getAccName());
            userTicketRelationDO.setTicketId(ticketId);
            userTicketRelationDO.setOperateTime(operateTime);
            userTicketRelationDO.setCreateBy("binlog");
            userTicketRelationDO.setModifyBy("binlog");
            list.add(userTicketRelationDO);
        }


        // 先清除数据
        UserTicketRelationDeleteArg deleteArg = new UserTicketRelationDeleteArg();
        deleteArg.setCorpCode(userInfoDO.getCorpCode());
        deleteArg.setUserId(userInfoDO.getId());
        dao.deleteByArg(deleteArg);

        // 然后重新保存
        dao.batchInsert(list);

        // 设置本次时间作为用户更新数据的缓存
        String cacheKey = OrganizationSystemCodes.USER_TICKET_RELATION_CACHE_KEY + userInfoDO.getId();
        cache.sync().setEx(cacheKey, DataTypeUtil.longToByte(operateTime.getTime()), TimeUnit.DAYS.toSeconds(1));
    }


    @Override
    public void refreshOperateTime(TicketSyncContext context) {
        log.debug("【UserTicketRelationUpdateComponent】 refreshOperateTime start");
        String corpCode = context.getCorpCode();
        Long ticketId = context.getTicketId();
        Date operateTime = context.getOperateTime();
        UserTicketRelationQueryArg queryArg = new UserTicketRelationQueryArg();
        queryArg.setCorpCode(corpCode);
        queryArg.setTicketId(ticketId);
        List<UserTicketRelationDO> userTicketRelationDOS = dao.queryByArg(queryArg);

        if (CollectionUtils.isEmpty(userTicketRelationDOS)) {
            log.debug("【UserTicketRelationUpdateComponent】 no user changed, return");
            return;
        }
        UserTicketRelationUpdateArg updateArg = new UserTicketRelationUpdateArg();
        updateArg.setCorpCode(corpCode);
        updateArg.setTicketId(ticketId);
        updateArg.setOperateTime(operateTime);
        updateArg.setModifyBy("binlog");
        dao.updateOperateTime(updateArg);

        for (UserTicketRelationDO userTicketRelationDO : userTicketRelationDOS) {
            // 设置本次时间作为用户更新数据的缓存
            String cacheKey = OrganizationSystemCodes.USER_TICKET_RELATION_CACHE_KEY + userTicketRelationDO.getUserId();
            cache.sync().setEx(cacheKey, DataTypeUtil.longToByte(operateTime.getTime()), TimeUnit.DAYS.toSeconds(1));
        }

    }
}
