package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.ClientTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.SaleModelEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractUpdateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 售票员收款方式更新参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktSellerPayawayUpdateParam extends AbstractUpdateParam<Long> {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 售票员
     */
    private String seller;

    /**
     * 账户ID
     */
    private Long accId;

    /**
     * 售票模式：1:正常出票 2:预售票 3:电子商务票 4:手工票补录 5:剧院售票6：自助机7:扫码入园
     */
    private SaleModelEnum saleModel;

    /**
     * 客户类型（1：散客 2：团队...）
     */
    private ClientTypeEnum clientType;

    /**
     * 支付方式
     */
    private String payAway;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 启用状态
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 支付ID
     */
    private Long payId;

    /**
     * 修改人
     */
    private String modifyBy;
} 