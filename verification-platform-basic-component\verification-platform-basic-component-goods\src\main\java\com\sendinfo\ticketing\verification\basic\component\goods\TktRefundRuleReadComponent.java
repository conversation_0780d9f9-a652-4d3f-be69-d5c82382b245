package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktRefundRuleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktRefundRule;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface TktRefundRuleReadComponent extends ReadComponent<Long, TktRefundRuleQueryParam, TktRefundRule> {

    /**
     * 根据票ID获取退票规则
     *
     * @param corpCode
     * @param ticketId
     * @return
     */
    TktRefundRule getTktRefundRuleByTicketId(String corpCode, Long ticketId);


    /**
     * 根据票ID集合获取退票规则
     *
     * @param ticketIdSet 票型ID集合
     * @param corpCode    企业编码
     * @return 退票规则集合
     */
    List<TktRefundRule> getTktRefundRuleByTicketIds(Set<Long> ticketIdSet, String corpCode);
} 