package com.sendinfo.ticketing.verification.basic.component.account.mapper;

import com.sendinfo.ticketing.verification.basic.api.account.request.CapitalAccountCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.account.request.CapitalAccountQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountCreateParam;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountQueryParam;
import com.sendinfo.ticketing.verification.basic.component.account.param.CapitalAccountUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.account.CapitalAccount;
import com.sendinfo.ticketing.verification.basic.repository.account.arg.CapitalAccountDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.account.arg.CapitalAccountQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.account.arg.CapitalAccountUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.account.dataobject.CapitalAccountDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 资金账户对象转换映射器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface CapitalAccountMapper {

    CapitalAccountMapper INSTANCE = Mappers.getMapper(CapitalAccountMapper.class);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    CapitalAccountQueryArg convert(CapitalAccountQueryParam queryParam);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryRequest 查询参数
     * @return 查询参数
     */
    CapitalAccountQueryParam convert(CapitalAccountQueryRequest queryRequest);

    /**
     * 数据对象转换为模型对象
     *
     * @param dataObject 数据对象
     * @return 模型对象
     */
    CapitalAccount convert(CapitalAccountDO dataObject);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    CapitalAccountDO convert(CapitalAccountCreateParam createParam);

    /**
     * 更新参数转换为数据对象
     *
     * @param updateParam 更新参数
     * @return 数据对象
     */
    CapitalAccountUpdateArg convert(CapitalAccountUpdateParam updateParam);

    /**
     * 删除参数转换为数据对象
     *
     * @param deleteParam 删除参数
     * @return 数据对象
     */
    CapitalAccountDeleteArg convert(CapitalAccountDeleteParam deleteParam);

    /**
     * 创建请求对象转换为数据对象
     *
     * @param createRequest 创建请求对象
     * @return  CapitalAccountCreateParam
     */
    CapitalAccountCreateParam convert(CapitalAccountCreateRequest createRequest);
} 