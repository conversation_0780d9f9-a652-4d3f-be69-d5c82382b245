package com.sendinfo.ticketing.verification.basic.component.customer.converter;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoCreateRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoDeptQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelInfoRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.customer.mapper.TravelInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoDeptQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoRoleQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelInfoDeptQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.TravelInfoRoleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.TravelInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

/**
 * 旅行社信息转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("travelInfoConverter")
public class TravelInfoConverter implements
        ReadDo2ModelConverter<TravelInfoDO, TravelInfo>,
        ReadParam2ArgConverter<TravelInfoQueryParam, TravelInfoQueryArg>,
        ReadReq2ParamConverter<TravelInfoQueryRequest, TravelInfoQueryParam>,
        CreateParam2DoConverter<TravelInfoCreateParam, TravelInfoDO>,
        CreateReq2ParamConverter<TravelInfoCreateRequest, TravelInfoCreateParam> {
    @Override
    public TravelInfo r_d2m(TravelInfoDO dataObject) {
        return TravelInfoMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public TravelInfoQueryArg r_p2a(TravelInfoQueryParam param) {
        return TravelInfoMapper.INSTANCE.convert(param);
    }

    public TravelInfoRoleQueryArg r_p2a(TravelInfoRoleQueryParam param) {
        return TravelInfoMapper.INSTANCE.convert(param);
    }

    public TravelInfoDeptQueryArg r_p2a(TravelInfoDeptQueryParam param) {
        return TravelInfoMapper.INSTANCE.convert(param);
    }

    @Override
    public TravelInfoQueryParam r_r2p(TravelInfoQueryRequest queryRequest) {
        return TravelInfoMapper.INSTANCE.convert(queryRequest);
    }

    public TravelInfoRoleQueryParam r_r2p(TravelInfoRoleQueryRequest queryRequest) {
        return TravelInfoMapper.INSTANCE.convert(queryRequest);
    }

    public TravelInfoDeptQueryParam r_r2p(TravelInfoDeptQueryRequest queryRequest) {
        return TravelInfoMapper.INSTANCE.convert(queryRequest);
    }

    @Override
    public TravelInfoDO c_p2d(TravelInfoCreateParam createParam) {
        return TravelInfoMapper.INSTANCE.convert(createParam);
    }

    @Override
    public TravelInfoCreateParam c_r2p(TravelInfoCreateRequest createRequest) {
        return TravelInfoMapper.INSTANCE.convert(createRequest);
    }
}