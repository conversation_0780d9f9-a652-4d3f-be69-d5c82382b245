package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerServiceCategoryQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerServiceCategory;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 服务类目信息读取服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ExplainerServiceCategoryReadService {

    /**
     * 模糊查询服务类目列表
     *
     * @param request 查询条件
     * @return 服务类目列表
     */
    ResultModel<List<ExplainerServiceCategory>> queryServiceCategoryList(ExplainerServiceCategoryQueryRequest request);
} 