package com.sendinfo.ticketing.verification.basic.api.pay.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/13 14:34
 */
@Getter
@Setter
@ToString(callSuper = true)
public class TktMainbodyBusQueryRequest implements Serializable {
    private static final long serialVersionUID = -4927866227374587856L;

    /**
     * 企业编码
     */
    @NotNull(message = "企业编码不能为空")
    private String corpCode;

    /**
     * 是否分组
     */
    private String groupFlag;

    /**
     * 业务ID
     */
    private Long busId;

    /**
     * 业务类型
     */
    private Integer busType;
}
