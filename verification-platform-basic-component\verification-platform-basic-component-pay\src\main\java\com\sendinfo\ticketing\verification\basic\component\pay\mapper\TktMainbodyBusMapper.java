package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.api.pay.request.TktMainbodyBusQueryRequest;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktMainbodyBusQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TktMainbodyBus;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.BusTypeEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktMainbodyBusQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktMainbodyBusDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Auther: chengjie.li
 * @Date: 2025/6/13 14:35
 */
@Mapper(
    imports = {BusTypeEnum.class}
)
public interface TktMainbodyBusMapper {
    TktMainbodyBusMapper INSTANCE = Mappers.getMapper(TktMainbodyBusMapper.class);

    // DO -> Model
    @Mapping(target = "busType", expression = "java(BusTypeEnum.ofCode(dataObject.getBusType()))")
    TktMainbodyBus convert(TktMainbodyBusDO dataObject);

    // QueryParam -> QueryArg
    @Mapping(target = "busType", expression = "java(param.getBusType() != null ? param.getBusType().getCode() : null)")
    TktMainbodyBusQueryArg convert(TktMainbodyBusQueryParam param);

    @Mapping(target = "busType", expression = "java(BusTypeEnum.ofCode(request.getBusType()))")
    TktMainbodyBusQueryParam convert(TktMainbodyBusQueryRequest request);
}
