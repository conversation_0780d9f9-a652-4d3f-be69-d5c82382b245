package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.MainbodyChanleMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.MainbodyChanle;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 商户渠道配置转换器
 * 实现各种对象间的转换逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("mainbodyChanleConverter")
public class MainbodyChanleConverter implements
        CreateParam2DoConverter<MainbodyChanleCreateParam, MainbodyChanleDO>,
        ReadDo2ModelConverter<MainbodyChanleDO, MainbodyChanle>,
        ReadParam2ArgConverter<MainbodyChanleQueryParam, MainbodyChanleQueryArg>,
        UpdateParam2ArgConverter<MainbodyChanleUpdateParam, MainbodyChanleUpdateArg, MainbodyChanle> {

    private final MainbodyChanleMapper mapper = MainbodyChanleMapper.INSTANCE;

    @Override
    public MainbodyChanleDO c_p2d(MainbodyChanleCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        return mapper.convert(createParam);
    }

    @Override
    public MainbodyChanle r_d2m(MainbodyChanleDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<MainbodyChanle> r_ds2ms(List<MainbodyChanleDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public MainbodyChanleQueryArg r_p2a(MainbodyChanleQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        MainbodyChanleQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }

    @Override
    public MainbodyChanleUpdateArg u_p2a(MainbodyChanleUpdateParam updateParam, MainbodyChanle currentModel) {
        if (updateParam == null) {
            return null;
        }
        return mapper.convert(updateParam);
    }
} 