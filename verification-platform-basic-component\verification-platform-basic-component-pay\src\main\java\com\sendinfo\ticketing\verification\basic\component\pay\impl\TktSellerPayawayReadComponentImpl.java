package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.TktSellerPayawayReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TktSellerPayawayConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TktSellerPayawayDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 售票员收款方式读取组件实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("tktSellerPayawayReadComponent")
@Getter
public class TktSellerPayawayReadComponentImpl implements
        TktSellerPayawayReadComponent,
        DaoBasedSingleRead<Long, TktSellerPayawayQueryParam, TktSellerPayaway, TktSellerPayawayDO>,
        DaoBasedListRead<Long, TktSellerPayawayQueryParam, TktSellerPayaway, TktSellerPayawayDO, TktSellerPayawayQueryArg>,
        DaoBasedCountRead<Long, TktSellerPayawayQueryParam, TktSellerPayaway, TktSellerPayawayQueryArg> {

    private final TktSellerPayawayDao dao;
    private final TktSellerPayawayConverter converter;

    public TktSellerPayawayReadComponentImpl(TktSellerPayawayDao dao, TktSellerPayawayConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TktSellerPayaway> queryEnableSellerPayAwayList(Long accId, Integer clientType, Integer saleModel, String corpCode) {
        return converter.r_ds2ms(dao.queryEnableSellerPayAwayList(accId, clientType, saleModel, corpCode));
    }
}