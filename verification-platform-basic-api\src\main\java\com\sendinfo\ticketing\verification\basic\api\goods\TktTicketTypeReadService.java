package com.sendinfo.ticketing.verification.basic.api.goods;

import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketType;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 门票类型读取服务
 *
 * <AUTHOR>
 */
public interface TktTicketTypeReadService {

    /**
     * 根据id查询门票类型
     *
     * @param id        主键id
     * @param corpCode  企业编码
     *
     * @return
     */
    ResultModel<TktTicketType> getTktTicketTypeById(Long id, String corpCode);

    /**
     * 批量查询门票类型
     *
     * @param ids
     * @return
     */
    ResultModel<List<TktTicketType>> batchQueryTicketTypeByIds(@NotEmpty List<Long> ids,@NotNull String corpCode);
} 