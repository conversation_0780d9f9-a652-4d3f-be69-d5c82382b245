package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.api.context.TicketSyncContext;
import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyCreateRequest;
import com.sendinfo.ticketing.verification.basic.component.goods.GoodsPropertyCreateComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.GoodsPropertyConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.GoodsPropertyCreateParam;
import com.sendinfo.ticketing.verification.basic.enums.TicketPropertyType;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.GoodsPropertyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.GoodsPropertyDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktClassInfoDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktTicketModelDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.GoodsPropertyDO;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktClassInfoDO;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketModelDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedBatchCreate;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleCreate;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

import static com.sendinfo.ticketing.verification.basic.enums.BaseInfoParameter.TICKET_KIND_NAME;

/**
 * <AUTHOR>
 * @since 2025-07-18 17:33
 */
@Slf4j
@Getter
@Component("goodsPropertyCreateComponent")
public class GoodsPropertyCreateComponentImpl implements GoodsPropertyCreateComponent,
        DaoBasedSingleCreate<Long, GoodsPropertyCreateParam, GoodsPropertyDO>,
        DaoBasedBatchCreate<Long, GoodsPropertyCreateParam, GoodsPropertyDO> {

    private final GoodsPropertyDao dao;
    private final GoodsPropertyConverter converter;
    private final TktTicketModelDao tktTicketModelDao;
    private final TktClassInfoDao tktClassInfoDao;

    public GoodsPropertyCreateComponentImpl(GoodsPropertyDao dao
            , GoodsPropertyConverter converter
            , TktTicketModelDao tktTicketModelDao
            , TktClassInfoDao tktClassInfoDao) {
        this.dao = dao;
        this.converter = converter;
        this.tktTicketModelDao = tktTicketModelDao;
        this.tktClassInfoDao = tktClassInfoDao;
    }

    @Override
    public Long batchInsert(List<GoodsPropertyCreateParam> paramList) {
        return (long) dao.batchInsert(converter.c_ps2ds(paramList));
    }

    @Override
    public void saveForTicketSync(TicketSyncContext context) {
        String corpCode = context.getCorpCode();
        TicketPropertyType type = context.getTicketPropertyType();
        Long typePk = context.getPk();
        Long goodPk = context.getTicketId();
        try {
            log.debug("【GoodsPropertyCreateComponent】 saveForTicketSync start,{}, ID = {}", type.getDescription(), typePk);
            GoodsPropertyQueryArg queryArg = new GoodsPropertyQueryArg();
            queryArg.setCorpCode(corpCode);
            queryArg.setGoodsPk(goodPk);
            queryArg.setType(type.getCode());
            queryArg.setTypePk(typePk);
            queryArg.setOperateTime(context.getOperateTime());
            int count = dao.countByArg(queryArg);

            // 数据库里的数据修改时间大于当前传过来的修改时间,表示后修改的数据已经更新完成,此次是前修改数据,直接过滤不操作
            if (count > 0) {
                log.debug("【GoodsPropertyCreateComponent】 {}{} already update.", type.getDescription(), typePk);
                return;
            }

            // 先处理删除
            GoodsPropertyDeleteArg deleteArg = new GoodsPropertyDeleteArg();
            deleteArg.setCorpCode(corpCode);
            deleteArg.setGoodsPk(goodPk);
            deleteArg.setType(type.getCode());
            deleteArg.setTypePk(typePk);
            count = dao.deleteByArg(deleteArg);
            log.debug("【GoodsPropertyCreateComponent】 {}{} deleted rows:{}", type.getDescription(), typePk, count);

            // 如果是删除的,直接结束
            if ("T".equalsIgnoreCase(context.getDeleted())) {
                return;
            }

            //保存新数据
            List<GoodsPropertyCreateRequest> goodsPropertyCreateRequestList = context.getGoodsPropertyCreateRequestList();

            if (!TicketPropertyType.BASE_INFO.equals(type)) {
                TktTicketModelDO tktTicketModelDO = tktTicketModelDao.selectOneById(goodPk, corpCode);
                if (Objects.isNull(tktTicketModelDO)) {
                    //未获取到票型基础信息,直接返回结束
                    log.error("【GoodsPropertyCreateComponent】 {}{} not find ticket model info", type.getDescription(), typePk);
                    return;
                }
                String goodsCode = tktTicketModelDO.getTicketCode();

                goodsPropertyCreateRequestList.forEach(inner -> inner.setGoodsCode(goodsCode));
            } else {
                // 添加票种名称字段
                if (StringUtils.isNotBlank(context.getTicketKind())) {
                    GoodsPropertyCreateRequest ticketKindNameRequest = getTicketKindName(corpCode, context.getTicketKind(), goodsPropertyCreateRequestList.get(0));
                    goodsPropertyCreateRequestList.add(ticketKindNameRequest);
                }
            }

            List<GoodsPropertyCreateParam> goodsPropertyCreateParamList = converter.c_rs2ps(goodsPropertyCreateRequestList);
            List<GoodsPropertyDO> list = converter.c_ps2ds(goodsPropertyCreateParamList);
            count = dao.batchInsert(list);
            log.debug("【GoodsPropertyCreateComponent】 {}{} insert rows:{}", type.getDescription(), typePk, count);
        } catch (Exception e) {
            log.error("【GoodsPropertyCreateComponent】 {}{} update exception:", type.getDescription(), typePk, e);
            throw e;
        }
    }

    /**
     * 补充票型票种名称
     *
     * @param corpCode    企业码
     * @param ticketKind  票种编码
     * @param baseRequest 入参
     */
    private GoodsPropertyCreateRequest getTicketKindName(String corpCode, String ticketKind, GoodsPropertyCreateRequest baseRequest) {
        String ticketKindName = "unknown";
        TktClassInfoDO tktClassInfoDO = tktClassInfoDao.selectOneByCode(ticketKind, corpCode);
        if (Objects.nonNull(tktClassInfoDO)) {
            ticketKindName = tktClassInfoDO.getClassName();
        }

        // 用列表的第一个,补充完整其他字段
        return GoodsPropertyCreateRequest.builder()
                .corpCode(baseRequest.getCorpCode())
                .goodsPk(baseRequest.getGoodsPk())
                .goodsCode(baseRequest.getGoodsCode())
                .domainCode(baseRequest.getDomainCode())
                .type(baseRequest.getType())
                .typePk(baseRequest.getTypePk())
                .key(TICKET_KIND_NAME.getCode())
                .description(TICKET_KIND_NAME.getDescription())
                .value(ticketKindName)
                .operateTime(baseRequest.getOperateTime())
                .build();
    }
}
