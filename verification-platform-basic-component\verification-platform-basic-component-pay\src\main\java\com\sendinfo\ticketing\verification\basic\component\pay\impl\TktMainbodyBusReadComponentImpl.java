package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.TktMainbodyBusReadComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TktMainbodyBusConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktMainbodyBusQueryParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TktMainbodyBus;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktMainbodyBusQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TktMainbodyBusDao;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktMainbodyBusDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;

import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 收入主体业务设置组件实现
 */
@Getter
@Component("tktMainbodyBusComponent")
public class TktMainbodyBusReadComponentImpl implements TktMainbodyBusReadComponent,
        DaoBasedSingleRead<Long, TktMainbodyBusQueryParam, TktMainbodyBus, TktMainbodyBusDO>,
        DaoBasedCountRead<Long, TktMainbodyBusQueryParam, TktMainbodyBus, TktMainbodyBusQueryArg>,
        DaoBasedListRead<Long, TktMainbodyBusQueryParam, TktMainbodyBus, TktMainbodyBusDO, TktMainbodyBusQueryArg> {

    private final TktMainbodyBusDao dao;

    private final TktMainbodyBusConverter converter;


    public TktMainbodyBusReadComponentImpl(TktMainbodyBusDao dao, TktMainbodyBusConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public TktMainbodyBus queryTktMainbodyBusByTicketId(Long ticketId, String corpCode) {
        TktMainbodyBusDO tktMainbodyBusDO = dao.queryTktMainbodyBusByTicketId(ticketId, corpCode);
        return Optional.ofNullable(tktMainbodyBusDO)
                .map(converter::r_d2m)
                .orElse(null);
    }
}