package com.sendinfo.ticketing.verification.basic.api.customer;

import com.sendinfo.ticketing.verification.basic.api.customer.request.TravelTaxQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelTax;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * travel_tax信息查询服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TravelTaxReadService {

    /**
     * 查询travel_tax信息
     *
     * @param id 主键
     * @return travel_tax信息
     */
    ResultModel<TravelTax> queryTravelTax(@NotNull Long id);

    /**
     * 批量查询travel_tax信息
     *
     * @param ids 主键集合
     * @return travel_tax信息列表
     */
    ResultModel<List<TravelTax>> batchQueryByIds(@NotEmpty Set<Long> ids);

    /**
     * 查询travel_tax信息列表
     *
     * @param queryRequest  travel_tax信息查询请求对象
     * @return  travel_tax信息列表
     */
    ResultModel<List<TravelTax>> queryTravelTaxList(TravelTaxQueryRequest queryRequest);
} 