package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysRole;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 系统角色读取服务接口
 * <AUTHOR>
 */
public interface SysRoleReadService {

    /**
     * 查询系统角色列表
     *
     * @param request 查询条件
     * @return 系统角色列表
     */
    ResultModel<List<SysRole>> querySysRoles(SysRoleQueryRequest request);
} 