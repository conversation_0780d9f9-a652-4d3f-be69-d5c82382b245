package com.sendinfo.ticketing.verification.basic.component.goods.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/26 17:30
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class TicketCalendarPriceQueryParam extends AbstractQueryParam {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 开始时间范围查询
     */
    private LocalDateTime beginTimeStart;
    private LocalDateTime beginTimeEnd;

    /**
     * 结束时间范围查询
     */
    private LocalDateTime endTimeStart;
    private LocalDateTime endTimeEnd;

    /**
     * 星期几查询
     */
    private String weeDay;
}
