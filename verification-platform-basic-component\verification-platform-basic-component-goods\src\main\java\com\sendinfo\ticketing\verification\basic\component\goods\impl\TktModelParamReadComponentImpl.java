package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktModelParamReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktModelParamConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktModelParamQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktModelParam;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktModelParamDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:18
 **/
@Getter
@Component("tktModelParamReadComponent")
public class TktModelParamReadComponentImpl implements TktModelParamReadComponent,
        DaoBasedSingleRead<Long, TktModelParamQueryParam, TktModelParam, TktModelParamDO>,
        DaoBasedCountRead<Long, TktModelParamQueryParam, TktModelParam, TktModelParamQueryArg>,
        DaoBasedListRead<Long, TktModelParamQueryParam, TktModelParam, TktModelParamDO, TktModelParamQueryArg> {
    private final TktModelParamDao dao;
    private final TktModelParamConverter converter;


    public TktModelParamReadComponentImpl(TktModelParamDao dao, TktModelParamConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TktModelParam> batchQueryTicketModelParamByIds(List<Long> ids, String corpCode) {
        List<TktModelParamDO> tktModelParamDOS = dao.batchQueryTicketModelParamByIds(ids, corpCode);
        return Optional.ofNullable(tktModelParamDOS)
                .map(converter::r_ds2ms)
                .orElse(Collections.emptyList());
    }
}
