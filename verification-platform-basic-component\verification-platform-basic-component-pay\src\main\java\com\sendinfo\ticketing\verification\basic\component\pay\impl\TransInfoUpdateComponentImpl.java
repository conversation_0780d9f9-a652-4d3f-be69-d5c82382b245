package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.TransInfoUpdateComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TransInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TransInfoDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleUpdate;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 交易记录更新组件实现
 *
 * @since 1.0.0
 */
@Component("transInfoUpdateComponent")
@Getter
public class TransInfoUpdateComponentImpl implements
        TransInfoUpdateComponent,
        DaoBasedSingleUpdate<TransInfoUpdateParam, TransInfoUpdateArg, TransInfo> {

    private final TransInfoDao dao;
    private final TransInfoConverter converter;

    public TransInfoUpdateComponentImpl(TransInfoDao dao, TransInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 