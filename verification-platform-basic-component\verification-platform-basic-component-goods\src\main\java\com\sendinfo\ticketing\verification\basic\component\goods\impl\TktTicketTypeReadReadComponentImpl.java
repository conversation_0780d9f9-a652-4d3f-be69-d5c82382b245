package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TktTicketTypeReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TktTicketTypeConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketTypeQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketType;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktTicketTypeQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TktTicketTypeDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktTicketTypeDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 门票类型组件实现类
 *
 * <AUTHOR>
 */
@Getter
@Component("tktTicketTypeComponent")
public class TktTicketTypeReadReadComponentImpl implements TktTicketTypeReadComponent,
        DaoBasedSingleRead<Long, TktTicketTypeQueryParam, TktTicketType, TktTicketTypeDO>,
        DaoBasedCountRead<Long, TktTicketTypeQueryParam, TktTicketType, TktTicketTypeQueryArg>,
        DaoBasedListRead<Long, TktTicketTypeQueryParam, TktTicketType, TktTicketTypeDO, TktTicketTypeQueryArg> {

    private final TktTicketTypeDao dao;
    private final TktTicketTypeConverter converter;

    public TktTicketTypeReadReadComponentImpl(TktTicketTypeDao dao, TktTicketTypeConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public TktTicketType getTktTicketTypeById(Long id, String corpCode) {
        TktTicketTypeDO tktTicketTypeDO = dao.getTktTicketTypeById(id, corpCode);
        return Optional.ofNullable(tktTicketTypeDO)
                .map(converter::r_d2m)
                .orElse(null);
    }

    @Override
    public List<TktTicketType> batchQueryTicketTypeByIds(List<Long> ids,String corpCode) {
        List<TktTicketTypeDO> tktTicketTypeDOS = dao.batchQueryTicketTypeByIds(ids,corpCode);
        return Optional.ofNullable(tktTicketTypeDOS)
                .map(tktTicketTypeDOList -> tktTicketTypeDOList.stream()
                        .map(converter::r_d2m)
                        .collect(Collectors.toList()))
                .orElse(null);
    }
}