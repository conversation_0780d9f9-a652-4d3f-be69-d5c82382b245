package com.sendinfo.ticketing.verification.basic.api.customer.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * travel_tax信息查询请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/16
 */
@Getter
@Setter
@ToString
public class TravelTaxCreateRequest implements Serializable {

    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 旅行社ID
     */
    private Long travelId;
    /**
     * 实际抬头
     */
    private String taxTitle;
    /**
     * 纳税人识别号
     */
    private String taxNo;
    /**
     * 开户银行
     */
    private String taxBank;
    /**
     * 银行账号
     */
    private String taxAccount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建人
     */
    private String createBy;
} 