package com.sendinfo.ticketing.verification.basic.api.infra.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> 2025-07-29 11:14:51
 */
@Getter
@Setter
@ToString
public class PwSysFunctionsMarkAsRequisitionRequest implements Serializable {

    private static final long serialVersionUID = 4082131410582438531L;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 菜单ID列表
     */
    @NotEmpty(message = "功能菜单ID列表不能为空")
    private List<Long> sysFunctionIds;

    /**
     * 修改人
     */
    @NotBlank(message = "修改人不能为空")
    private String modifyBy;
}
