package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.TktSellerPayawayMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TktSellerPayawayUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TktSellerPayaway;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TktSellerPayawayUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TktSellerPayawayDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import com.sendinfo.ticketing.verification.common.repository.arg.StatusUpdater;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 售票员收款方式转换器
 * 实现各种对象间的转换逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("tktSellerPayawayConverter")
public class TktSellerPayawayConverter implements
        CreateParam2DoConverter<TktSellerPayawayCreateParam, TktSellerPayawayDO>,
        ReadDo2ModelConverter<TktSellerPayawayDO, TktSellerPayaway>,
        ReadParam2ArgConverter<TktSellerPayawayQueryParam, TktSellerPayawayQueryArg>,
        UpdateParam2ArgConverter<TktSellerPayawayUpdateParam, TktSellerPayawayUpdateArg, TktSellerPayaway> {

    private final TktSellerPayawayMapper mapper = TktSellerPayawayMapper.INSTANCE;

    @Override
    public TktSellerPayawayDO c_p2d(TktSellerPayawayCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        TktSellerPayawayDO dataObject = mapper.convert(createParam);
        // 可以在此添加MapStruct难以处理的额外逻辑
        return dataObject;
    }

    @Override
    public TktSellerPayaway r_d2m(TktSellerPayawayDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<TktSellerPayaway> r_ds2ms(List<TktSellerPayawayDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public TktSellerPayawayQueryArg r_p2a(TktSellerPayawayQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        TktSellerPayawayQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }

    @Override
    public TktSellerPayawayUpdateArg u_p2a(TktSellerPayawayUpdateParam updateParam, TktSellerPayaway currentModel) {
        if (updateParam == null) {
            return null;
        }
        TktSellerPayawayUpdateArg updateArg = mapper.convert(updateParam);

        Optional.ofNullable(updateParam.getUseFlag())
                .ifPresent(innerStatus -> updateArg.setUseFlagUpdater(StatusUpdater.transfer(currentModel.getUseFlag().getCode(), innerStatus.getCode())));
        return updateArg;
    }
} 