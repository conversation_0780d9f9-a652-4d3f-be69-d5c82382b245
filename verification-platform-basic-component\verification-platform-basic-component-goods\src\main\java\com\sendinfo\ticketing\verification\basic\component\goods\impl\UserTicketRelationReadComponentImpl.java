package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.UserTicketRelationReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.UserTicketRelationConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.UserTicketRelation;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.UserTicketRelationQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.UserTicketRelationDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.UserTicketRelationDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-24 18:03
 */
@Slf4j
@Getter
@Component("userTicketRelationReadComponent")
public class UserTicketRelationReadComponentImpl implements UserTicketRelationReadComponent,
        DaoBasedSingleRead<Long, UserTicketRelationQueryParam, UserTicketRelation, UserTicketRelationDO>,
        DaoBasedCountRead<Long, UserTicketRelationQueryParam, UserTicketRelation, UserTicketRelationQueryArg>,
        DaoBasedListRead<Long, UserTicketRelationQueryParam, UserTicketRelation, UserTicketRelationDO, UserTicketRelationQueryArg> {

    private final UserTicketRelationDao dao;
    private final UserTicketRelationConverter converter;

    public UserTicketRelationReadComponentImpl(UserTicketRelationDao dao
            , UserTicketRelationConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<UserTicketRelation> queryListByUserId(Long userId, String corpCode) {
        List<UserTicketRelationDO> userTicketRelationDOS =  dao.queryListByUserId(userId, corpCode);
        return converter.r_ds2ms(userTicketRelationDOS);
    }

    @Override
    public Date getMaxOperateTime(Long userId) {
        return dao.getMaxOperateTime(userId);
    }
}
