package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 交易类型枚举
 * 0=支付, 1=退款
 */
@Getter
public enum TransTypeEnum {
    PAY(0, "支付"),
    REFUND(1, "退款");

    @JsonValue
    private final int code;
    private final String description;

    TransTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    private static final Map<Integer, TransTypeEnum> CODE_MAP =
            Arrays.stream(values()).collect(Collectors.toMap(TransTypeEnum::getCode, Function.identity()));

    @JsonCreator
    public static TransTypeEnum ofCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }
} 