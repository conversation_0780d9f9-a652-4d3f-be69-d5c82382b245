/**
 * System parameter group component relation read service
 *
 * <AUTHOR> 2025-01-24 15:30:00
 */
package com.sendinfo.ticketing.verification.basic.api.infra;

import com.sendinfo.ticketing.verification.basic.api.infra.request.SysParamGroupComponentRelationQueryCondition;
import com.sendinfo.ticketing.verification.basic.model.infra.SysParamGroupComponentRelation;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 系统参数分组组件关联查询服务
 */
public interface SysParamGroupComponentRelationReadService {

    /**
     * 分页查询系统参数分组组件关联
     *
     * @param pageRequest 分页查询条件
     * @return 分页结果
     */
    PageResultModel<SysParamGroupComponentRelation> queryPageList(
            PageRequest<SysParamGroupComponentRelationQueryCondition> pageRequest);

    /**
     * 根据ID查询系统参数分组组件关联
     *
     * @param id 关联ID
     * @return 关联信息
     */
    ResultModel<SysParamGroupComponentRelation> queryById(Long id);

}