package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.SysMainbodyManageSubMerchantsMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 经营主体子商户转换器
 * 实现各种对象间的转换逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyManageSubMerchantsConverter")
public class SysMainbodyManageSubMerchantsConverter implements
        CreateParam2DoConverter<SysMainbodyManageSubMerchantsCreateParam, SysMainbodyManageSubMerchantsDO>,
        ReadDo2ModelConverter<SysMainbodyManageSubMerchantsDO, SysMainbodyManageSubMerchants>,
        ReadParam2ArgConverter<SysMainbodyManageSubMerchantsQueryParam, SysMainbodyManageSubMerchantsQueryArg>,
        UpdateParam2ArgConverter<SysMainbodyManageSubMerchantsUpdateParam, SysMainbodyManageSubMerchantsUpdateArg, SysMainbodyManageSubMerchants> {

    private final SysMainbodyManageSubMerchantsMapper mapper = SysMainbodyManageSubMerchantsMapper.INSTANCE;

    @Override
    public SysMainbodyManageSubMerchantsDO c_p2d(SysMainbodyManageSubMerchantsCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        return mapper.convert(createParam);
    }

    @Override
    public SysMainbodyManageSubMerchants r_d2m(SysMainbodyManageSubMerchantsDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<SysMainbodyManageSubMerchants> r_ds2ms(List<SysMainbodyManageSubMerchantsDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public SysMainbodyManageSubMerchantsQueryArg r_p2a(SysMainbodyManageSubMerchantsQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        SysMainbodyManageSubMerchantsQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }

    @Override
    public SysMainbodyManageSubMerchantsUpdateArg u_p2a(SysMainbodyManageSubMerchantsUpdateParam updateParam, SysMainbodyManageSubMerchants currentModel) {
        if (updateParam == null) {
            return null;
        }
        return mapper.convert(updateParam);
    }
} 