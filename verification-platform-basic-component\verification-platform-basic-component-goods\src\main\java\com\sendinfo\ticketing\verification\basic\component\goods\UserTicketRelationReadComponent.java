package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.UserTicketRelationQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.UserTicketRelation;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-18 16:13
 */
public interface UserTicketRelationReadComponent extends ReadComponent<Long, UserTicketRelationQueryParam, UserTicketRelation> {

    /**
     * 根据用户ID查询
     * @param userId
     * @param corpCode
     * @return
     */
    List<UserTicketRelation> queryListByUserId(Long userId, String corpCode);

    Date getMaxOperateTime(Long userId);
}
