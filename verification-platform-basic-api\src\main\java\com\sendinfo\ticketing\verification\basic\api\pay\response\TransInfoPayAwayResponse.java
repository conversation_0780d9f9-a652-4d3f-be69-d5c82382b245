package com.sendinfo.ticketing.verification.basic.api.pay.response;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 交易记录支付方式响应对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/29 10:45
 */
@Data
public class TransInfoPayAwayResponse implements Serializable {

	/**
	 * 企业编码
	 */
	private String corpCode;
	/**
	 * 交易编号
	 */
	private String transNo;
	/**
	 * 交易类型
	 */
	private Integer transType;
	/**
	 * 订单号
	 */
	private String tradeCode;
	/**
	 * 支付方式
	 */
	private String payAway;
	/**
	 * 业务类型
	 */
	private String businessType;
	/**
	 * 交易金额
	 */
	private BigDecimal payAmount;
	/**
	 * 交易状态
	 */
	private String payStatus;
	/**
	 * 交易时间
	 */
	private Date payTime;

}
