package com.sendinfo.ticketing.verification.basic.repository.infra.dao;

import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwSysFunctionsBatchUpdateAsRequisitionArg;
import com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwSysFunctionsDO;
import com.sendinfo.ticketing.verification.common.repository.BatchInsertDAO;
import com.sendinfo.ticketing.verification.common.repository.CountableDAO;
import com.sendinfo.ticketing.verification.common.repository.GenericDAO;
import com.sendinfo.ticketing.verification.common.repository.PageableDAO;

import java.util.List;

/**
 * 系统功能模块数据访问接口
 *
 * <AUTHOR> 2025-07-24
 */
public interface PwSysFunctionsDao
        extends GenericDAO<Long, PwSysFunctionsDO, PwSysFunctionsUpdateArg, PwSysFunctionsDeleteArg>,
        CountableDAO<PwSysFunctionsQueryArg>,
        PageableDAO<PwSysFunctionsQueryArg, PwSysFunctionsDO>,
        BatchInsertDAO<Long, PwSysFunctionsDO> {

    /**
     * 根据子系统ID查询功能模块列表
     *
     * @param subsystemId 子系统ID
     * @return 功能模块列表
     */
    List<PwSysFunctionsDO> queryBySubSystemId(Integer subsystemId);

    /**
     * 根据父ID列表查询功能模块列表
     *
     * @param parentIds 父ID列表
     * @return 功能模块列表
     */
    List<PwSysFunctionsDO> queryByParentIds(List<Long> parentIds);

    /**
     * 查询已征用的功能模块列表
     *
     * @param corpCode 公司代码
     * @return 已征用的功能模块列表
     */
    List<PwSysFunctionsDO> queryHasRequisitionList(String corpCode);

    /**
     * 批量更新功能模块为已征用状态
     *
     * @param arg 批量更新参数
     * @return 更新的记录数
     */
    int batchUpdateAsRequisition(PwSysFunctionsBatchUpdateAsRequisitionArg arg);
}
