package com.sendinfo.ticketing.verification.basic.component.goods.converter;

import com.sendinfo.ticketing.verification.basic.component.goods.mapper.TktModelParamMapper;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TktModelParamQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktModelParam;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TktModelParamQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TktModelParamDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:21
 **/
@Component("tktModelParamConverter")
public class TktModelParamConverter implements ReadParam2ArgConverter<TktModelParamQueryParam, TktModelParamQueryArg>,
        ReadDo2ModelConverter<TktModelParamDO, TktModelParam> {

    @Override
    public TktModelParam r_d2m(TktModelParamDO dataObject) {
        return TktModelParamMapper.INSTANCE.convert(dataObject);
    }

    @Override
    public TktModelParamQueryArg r_p2a(TktModelParamQueryParam param) {
        return TktModelParamMapper.INSTANCE.convert(param);
    }
}
