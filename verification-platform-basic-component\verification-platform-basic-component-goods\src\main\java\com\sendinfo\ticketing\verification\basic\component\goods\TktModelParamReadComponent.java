package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktModelParamQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktModelParam;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/7/28 18:11
 **/
public interface TktModelParamReadComponent extends ReadComponent<Long, TktModelParamQueryParam, TktModelParam> {

    List<TktModelParam> batchQueryTicketModelParamByIds(List<Long> ids, String corpCode);
}
