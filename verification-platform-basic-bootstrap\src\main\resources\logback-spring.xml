<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <property name="APP_NAME" value="verification-platform-basic"/>
    <property name="LOG_PATH" value="${LOG_PATH:-${user.home}/logs/${APP_NAME}}"/>
    <property name="LOG_FILE" value="${LOG_PATH}/application.log"/>

    <property name="VERIFICATION_PLATFORM_BASIC_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p},%m%n"/>
    <property name="VERIFICATION_PLATFORM_BASIC_XFLUSH_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS},%m%n"/>
    <include resource="paas/sendinfo/cache/client/sendinfo-paas-cache-client-log.xml"/>
    <include resource="paas/sendinfo/message/client/sendinfo-paas-message-client-log.xml"/>
    <include resource="paas/sendinfo/rds/client/sendinfo-paas-rds-client-log.xml"/>


    <appender name="APPLICATION" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>${VERIFICATION_PLATFORM_BASIC_LOG_PATTERN}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${VERIFICATION_PLATFORM_BASIC_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="APPLICATION"/>
    </root>
</configuration>