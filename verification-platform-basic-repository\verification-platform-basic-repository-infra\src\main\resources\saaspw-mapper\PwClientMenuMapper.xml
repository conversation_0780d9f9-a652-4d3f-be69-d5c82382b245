<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sendinfo.ticketing.verification.basic.repository.infra.dao.PwClientMenuDao">

    <!-- ResultMap -->
    <resultMap id="BaseResultMap" type="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwClientMenuDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="corp_code" property="corpCode" jdbcType="VARCHAR"/>
        <result column="business_type" property="businessType" jdbcType="INTEGER"/>
        <result column="menu_name" property="menuName" jdbcType="VARCHAR"/>
        <result column="menu_code" property="menuCode" jdbcType="VARCHAR"/>
        <result column="icon_img" property="iconImg" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="menu_url" property="menuUrl" jdbcType="VARCHAR"/>
        <result column="wpf_url" property="wpfUrl" jdbcType="VARCHAR"/>
        <result column="sys_code" property="sysCode" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="INTEGER"/>
        <result column="parent_ids" property="parentIds" jdbcType="VARCHAR"/>
        <result column="function_type" property="functionType" jdbcType="VARCHAR"/>
        <result column="subsystem_id" property="subsystemId" jdbcType="INTEGER"/>
        <result column="style_file_url" property="styleFileUrl" jdbcType="VARCHAR"/>
        <result column="requisition_flag" property="requisitionFlag" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="modify_by" property="modifyBy" jdbcType="VARCHAR"/>
        <result column="deleted" property="deleted" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 表名 -->
    <sql id="tableName">client_menu</sql>

    <!-- Reusable Column List -->
    <sql id="columns">
        `id`,
        `corp_code`,
        `business_type`,
        `menu_name`,
        `menu_code`,
        `icon_img`,
        `title`,
        `menu_url`,
        `wpf_url`,
        `sys_code`,
        `sort_order`,
        `status`,
        `remark`,
        `parent_id`,
        `parent_ids`,
        `function_type`,
        `subsystem_id`,
        `style_file_url`,
        `requisition_flag`,
        `create_time`,
        `modify_time`,
        `create_by`,
        `modify_by`,
        `deleted`
    </sql>

    <!-- insert -->
    <insert id="insert" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.dataobject.PwClientMenuDO"
            useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        insert into <include refid="tableName"/> (
            `corp_code`,
            `business_type`,
            `menu_name`,
            `menu_code`,
            `icon_img`,
            `title`,
            `menu_url`,
            `wpf_url`,
            `sys_code`,
            `sort_order`,
            `status`,
            `remark`,
            `parent_id`,
            `parent_ids`,
            `function_type`,
            `subsystem_id`,
            `style_file_url`,
            `requisition_flag`,
            `create_by`,
            `modify_by`,
            `deleted`
        )
        values (
            #{corpCode,jdbcType=VARCHAR},
            #{businessType,jdbcType=INTEGER},
            #{menuName,jdbcType=VARCHAR},
            #{menuCode,jdbcType=VARCHAR},
            #{iconImg,jdbcType=VARCHAR},
            #{title,jdbcType=VARCHAR},
            #{menuUrl,jdbcType=VARCHAR},
            #{wpfUrl,jdbcType=VARCHAR},
            #{sysCode,jdbcType=VARCHAR},
            #{sortOrder,jdbcType=INTEGER},
            #{status,jdbcType=VARCHAR},
            #{remark,jdbcType=VARCHAR},
            #{parentId,jdbcType=INTEGER},
            #{parentIds,jdbcType=VARCHAR},
            #{functionType,jdbcType=VARCHAR},
            #{subsystemId,jdbcType=INTEGER},
            #{styleFileUrl,jdbcType=VARCHAR},
            #{requisitionFlag,jdbcType=INTEGER},
            #{createBy,jdbcType=VARCHAR},
            #{modifyBy,jdbcType=VARCHAR},
            #{deleted,jdbcType=VARCHAR}
        )
    </insert>

    <!-- queryById -->
    <select id="queryById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `id` = #{id,jdbcType=BIGINT} and `deleted` = 'F'
    </select>

    <!-- updateByArg -->
    <update id="updateByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuUpdateArg">
        update <include refid="tableName"/>
        <set>
            <if test="corpCode != null">
                `corp_code` = #{corpCode,jdbcType=VARCHAR},
            </if>
            <if test="requisitionFlag != null">
                `requisition_flag` = #{requisitionFlag,jdbcType=INTEGER},
            </if>
            <if test="modifyBy != null">
                `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            </if>
            `modify_time` = CURRENT_TIMESTAMP
        </set>
        where `id` = #{id,jdbcType=BIGINT}
        and `deleted` = 'F'
    </update>

    <!-- softDeleteByArg -->
    <update id="softDeleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuDeleteArg">
        update <include refid="tableName"/>
        set `deleted` = 'T',
            `modify_by` = #{modifyBy,jdbcType=VARCHAR},
            `modify_time` = now()
        where `id` = #{id,jdbcType=BIGINT}
    </update>

    <!-- deleteByArg (物理删除) -->
    <delete id="deleteByArg" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuDeleteArg">
        DELETE FROM <include refid="tableName"/>
        where `id` = #{id,jdbcType=BIGINT}
    </delete>

    <!-- countByArg -->
    <select id="countByArg" resultType="java.lang.Integer" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuQueryArg">
        select count(1)
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            and `status` = 'T'
            and `PARENT_ID` is null
            and (`REQUISITION_FLAG`=0 or `REQUISITION_FLAG` is null)
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null">
                and `business_type` = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="menuName != null">
                and `menu_name` like concat('%', #{menuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="menuCode != null">
                and `menu_code` = #{menuCode,jdbcType=VARCHAR}
            </if>
            <if test="functionType != null">
                and `function_type` = #{functionType,jdbcType=VARCHAR}
            </if>
            <if test="subsystemId != null">
                and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
            </if>
        </where>
    </select>

    <!-- queryByArg -->
    <select id="queryByArg" resultMap="BaseResultMap" parameterType="com.sendinfo.ticketing.verification.basic.repository.infra.arg.PwClientMenuQueryArg">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        <where>
            and `deleted` = 'F'
            and `status` = 'T'
            and `PARENT_ID` is null
            and (`REQUISITION_FLAG`=0 or `REQUISITION_FLAG` is null)
            <if test="corpCode != null">
                and `corp_code` = #{corpCode,jdbcType=VARCHAR}
            </if>
            <if test="businessType != null">
                and `business_type` = #{businessType,jdbcType=INTEGER}
            </if>
            <if test="menuName != null">
                and `menu_name` like concat('%', #{menuName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="menuCode != null">
                and `menu_code` = #{menuCode,jdbcType=VARCHAR}
            </if>
            <if test="functionType != null">
                and `function_type` = #{functionType,jdbcType=VARCHAR}
            </if>
            <if test="subsystemId != null">
                and `subsystem_id` = #{subsystemId,jdbcType=INTEGER}
            </if>
        </where>
        <if test="sortItems != null and !sortItems.isEmpty()">
            order by
            <foreach collection="sortItems" separator="," item="sortItem">
                ${sortItem.column} ${sortItem.sortType}
            </foreach>
        </if>
        <if test="offset != null and limit != null">
            limit #{offset}, #{limit}
        </if>
    </select>

    <!-- queryByParentIds -->
    <select id="queryByParentIds" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `parent_id` in
        <foreach collection="parentIds" item="parentId" open="(" separator="," close=")">
            #{parentId,jdbcType=BIGINT}
        </foreach>
        and `deleted` = 'F'
        order by `sort_order` asc, `id` asc
    </select>

    <!-- queryHasRequisitionList -->
    <select id="queryHasRequisitionList" resultMap="BaseResultMap">
        select
        <include refid="columns"/>
        from <include refid="tableName"/>
        where `requisition_flag` = 1
        and `deleted` = 'F'
        <if test="corpCode != null">
            and `corp_code` = #{corpCode,jdbcType=VARCHAR}
        </if>
        order by `sort_order` asc, `id` asc
    </select>

    <!-- batchUpdateAsRequisition -->
    <update id="batchUpdateAsRequisition" parameterType="java.util.Map">
        update <include refid="tableName"/>
        set `requisition_flag` = 1,
            `modify_time` = now(),
            `modify_by` = #{modifyBy,jdbcType=VARCHAR}
        where `id` in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id,jdbcType=BIGINT}
        </foreach>
        <if test="corpCode != null">
            and `corp_code` = #{corpCode,jdbcType=VARCHAR}
        </if>
        and `deleted` = 'F'
    </update>

</mapper>
