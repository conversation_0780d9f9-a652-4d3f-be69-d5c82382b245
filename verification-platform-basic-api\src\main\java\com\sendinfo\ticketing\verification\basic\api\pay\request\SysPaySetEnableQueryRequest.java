package com.sendinfo.ticketing.verification.basic.api.pay.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Set;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/25 11:31
 */
@Getter
@Setter
public class SysPaySetEnableQueryRequest implements Serializable {
    private static final long serialVersionUID = -5102050258032607469L;

    /**
     * 支付类型
     */
    private String payType;

    /**
     * 企业编码
     */
    @NotEmpty
    private String corpCode;

    /**
     * 支付方式ID集合
     */
    private Set<Long> idSet;
}
