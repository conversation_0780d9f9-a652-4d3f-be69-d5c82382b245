package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageSubMerchantsUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageSubMerchantsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 经营主体子商户映射接口
 * 使用MapStruct进行对象映射转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysMainbodyManageSubMerchantsMapper {

    SysMainbodyManageSubMerchantsMapper INSTANCE = Mappers.getMapper(SysMainbodyManageSubMerchantsMapper.class);

    /**
     * 创建参数转换为数据对象
     */
    SysMainbodyManageSubMerchantsDO convert(SysMainbodyManageSubMerchantsCreateParam createParam);

    /**
     * 数据对象转换为模型
     */
    SysMainbodyManageSubMerchants convert(SysMainbodyManageSubMerchantsDO dataObject);

    /**
     * 查询参数转换为查询参数
     */
    SysMainbodyManageSubMerchantsQueryArg convert(SysMainbodyManageSubMerchantsQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     */
    SysMainbodyManageSubMerchantsUpdateArg convert(SysMainbodyManageSubMerchantsUpdateParam updateParam);
} 