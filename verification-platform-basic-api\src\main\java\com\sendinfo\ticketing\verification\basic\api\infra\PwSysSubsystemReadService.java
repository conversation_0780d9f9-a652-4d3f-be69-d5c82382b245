package com.sendinfo.ticketing.verification.basic.api.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.model.infra.PwSysSubsystem;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

/**
 * 子系统管理查询服务
 *
 * <AUTHOR> 2025-07-24 15:40:00
 */
public interface PwSysSubsystemReadService {

    /**
     * 查询所有启用且未删除的子系统
     *
     * @return 子系统列表
     */
    ResultModel<List<PwSysSubsystem>> queryAllInUseList(String corpCode);

}