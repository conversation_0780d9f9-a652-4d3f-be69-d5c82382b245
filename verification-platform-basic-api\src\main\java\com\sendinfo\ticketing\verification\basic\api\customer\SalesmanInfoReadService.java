package com.sendinfo.ticketing.verification.basic.api.customer;

import com.sendinfo.ticketing.verification.basic.api.customer.request.SalesmanInfoQueryEnabledByIdsRequest;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;

/**
 * 业务员信息读取服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SalesmanInfoReadService {

    /**
     * 根据ID集合批量查询启用的业务员信息
     *
     * @param request 查询请求参数
     * @return 启用的业务员信息列表
     */
    ResultModel<List<SalesmanInfo>> queryEnabledByIds(SalesmanInfoQueryEnabledByIdsRequest request);
} 