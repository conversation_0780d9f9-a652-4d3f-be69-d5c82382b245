package com.sendinfo.ticketing.verification.basic.api.context;

import com.sendinfo.ticketing.verification.basic.api.goods.request.GoodsPropertyCreateRequest;
import com.sendinfo.ticketing.verification.basic.enums.TicketPropertyType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-07-22 14:15
 */
@Getter
@Setter
@ToString
public class TicketSyncContext {

    private Boolean success = false;

    /**
     * 票型相关信息表ID
     */
    private Long pk;

    /**
     * 对应票型基础信息表ID
     */
    private Long ticketId;


    /**
     * 对应票型编码
     */
    private String ticketCode;

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 是否删除
     */
    private String deleted;

    /**
     * 同步数据源的数据修改时间,用于过滤先操作后到的请求
     */
    private Date operateTime = new Date();

    /**
     * 当前票型相关信息类型
     */
    private TicketPropertyType ticketPropertyType;

    /**
     * 需要插入的票型相关类型扩展信息字段列表
     */
    private List<GoodsPropertyCreateRequest> goodsPropertyCreateRequestList;


    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 角色ID
     */
    private Long sysRoleId;

    /**
     * 票型票种编码,用于保存票种名称
     */
    private String ticketKind;

}
