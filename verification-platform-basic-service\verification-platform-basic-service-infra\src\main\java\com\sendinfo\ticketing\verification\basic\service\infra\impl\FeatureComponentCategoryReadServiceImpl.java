package com.sendinfo.ticketing.verification.basic.service.infra.impl;

import com.sendinfo.ticketing.verification.basic.api.infra.FeatureComponentCategoryReadService;
import com.sendinfo.ticketing.verification.basic.api.infra.request.FeatureComponentCategoryQueryCondition;

import com.sendinfo.ticketing.verification.basic.model.infra.FeatureComponentCategory;
import com.sendinfo.ticketing.verification.basic.model.infra.error.InfraErrorDef;
import com.sendinfo.ticketing.verification.basic.service.common.enums.CommonAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.infra.enums.FeatureComponentCategoryAttachmentKey;
import com.sendinfo.ticketing.verification.basic.service.infra.function.QueryFeatureComponentCategoryFunction;
import com.sendinfo.ticketing.verification.common.api.request.PageRequest;
import com.sendinfo.ticketing.verification.common.api.result.PageResultModel;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import com.sendinfo.ticketing.verification.common.api.result.Results;
import com.sendinfo.ticketing.verification.flow.FlowAgentBuilder;
import com.sendinfo.ticketing.verification.common.service.support.ApiCommonFailedBiActions;
import com.sendinfo.ticketing.verification.basic.exception.VerificationBizRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * FeatureComponentCategory读取服务实现
 *
 * <AUTHOR> 2025-07-28 15:30:00
 */
@Service("featureComponentCategoryReadService")
@Slf4j
public class FeatureComponentCategoryReadServiceImpl implements FeatureComponentCategoryReadService {

    private final FlowAgentBuilder flowAgentBuilder;
    private final QueryFeatureComponentCategoryFunction queryFeatureComponentCategoryFunction;

    public FeatureComponentCategoryReadServiceImpl(FlowAgentBuilder flowAgentBuilder,
            QueryFeatureComponentCategoryFunction queryFeatureComponentCategoryFunction) {
        this.flowAgentBuilder = flowAgentBuilder;
        this.queryFeatureComponentCategoryFunction = queryFeatureComponentCategoryFunction;
    }

    @Override
    public PageResultModel<FeatureComponentCategory> queryPageList(
            PageRequest<FeatureComponentCategoryQueryCondition> request) {
        return flowAgentBuilder
                .<PageRequest<FeatureComponentCategoryQueryCondition>, PageResultModel<FeatureComponentCategory>>validateThenChooseBuilder()
                .appendLogicAction(queryFeatureComponentCategoryFunction.getQueryPageByCondition())
                .withSuccessfulAction(q -> {
                    List<FeatureComponentCategory> data = q.getAttachment(
                            FeatureComponentCategoryAttachmentKey.FEATURE_COMPONENT_CATEGORY_DATA_LIST_ATTACHMENT_KEY);
                    int total = q.getAttachment(
                            FeatureComponentCategoryAttachmentKey.FEATURE_COMPONENT_CATEGORY_DATA_COUNT_ATTACHMENT_KEY);
                    return Results.successPage(data, total, request.getPageSize(), request.getCurrentPage());
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToPageResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[FeatureComponentCategoryReadServiceImpl] queryPageList error, request: {}", request,
                            th);
                    return Results.failPage(InfraErrorDef.QUERY_FEATURE_COMPONENT_CATEGORY_ERROR);
                })
                .rethrowException(VerificationBizRuntimeException.class::isInstance)
                .build()
                .prompt(request)
                .getResult();
    }

    @Override
    public ResultModel<FeatureComponentCategory> queryFeatureComponentCategoryById(Long id) {
        return flowAgentBuilder.<Long, ResultModel<FeatureComponentCategory>>validateThenChooseBuilder()
                .appendLogicAction(queryFeatureComponentCategoryFunction.getQuerySingleById())
                .withSuccessfulAction(q -> {
                    FeatureComponentCategory category = q.getAttachment(
                            FeatureComponentCategoryAttachmentKey.FEATURE_COMPONENT_CATEGORY_ATTACHMENT_KEY);
                    return Results.success(category);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error(
                            "[FeatureComponentCategoryReadServiceImpl] queryFeatureComponentCategoryById error, id: {}",
                            id, th);
                    return Results.fail(InfraErrorDef.QUERY_FEATURE_COMPONENT_CATEGORY_ERROR);
                })
                .rethrowException(VerificationBizRuntimeException.class::isInstance)
                .build()
                .prompt(id)
                .getResult();
    }

    @Override
    public ResultModel<List<FeatureComponentCategory>> queryAllList(String corpCode) {
        return flowAgentBuilder.<String, ResultModel<List<FeatureComponentCategory>>>validateThenChooseBuilder()
                .appendLogicAction(queryFeatureComponentCategoryFunction::queryAllList)
                .withSuccessfulAction(q -> {
                    List<FeatureComponentCategory> list = q.getAttachment(
                            FeatureComponentCategoryAttachmentKey.FEATURE_COMPONENT_CATEGORY_DATA_LIST_ATTACHMENT_KEY);
                    return Results.success(list);
                })
                .withFailedBiAction(ApiCommonFailedBiActions::reasonMappingToResult)
                .withExceptionBiAction((q, th) -> {
                    log.error("[FeatureComponentCategoryReadServiceImpl] queryAllList error, corpCode: {}", corpCode,
                            th);
                    return Results.fail(InfraErrorDef.QUERY_FEATURE_COMPONENT_CATEGORY_ERROR);
                })
                .rethrowException(VerificationBizRuntimeException.class::isInstance)
                .build()
                .prompt(corpCode)
                .getResult();
    }
}
