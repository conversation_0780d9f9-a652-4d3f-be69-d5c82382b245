package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体查询参数
 * 用于封装查询经营主体所需的参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyQueryParam extends AbstractPageQueryParam {

    /**
     * 企业码
     */
    private String corpCode;

    /**
     * 经营主体名称（模糊查询）
     */
    private String mainbodyName;

    /**
     * 经营主体简称（模糊查询）
     */
    private String mainbodyShortName;

    /**
     * 主体商户号（精确查询）
     */
    private String merchantNo;

    /**
     * 联系人（模糊查询）
     */
    private String linkMan;

    /**
     * 联系电话（精确查询）
     */
    private String linkTel;

    /**
     * 备注（模糊查询）
     */
    private String remark;

    /**
     * 经营主体编号（精确查询）
     */
    private String mainbodyNumber;

    /**
     * 部门ID（精确查询）
     */
    private Long deptId;
} 