package com.sendinfo.ticketing.verification.basic.component.pay.impl;

import com.sendinfo.ticketing.verification.basic.component.pay.TransInfoDeleteComponent;
import com.sendinfo.ticketing.verification.basic.component.pay.converter.TransInfoConverter;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoDeleteParam;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dao.TransInfoDao;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleDelete;
import lombok.Getter;
import org.springframework.stereotype.Component;

/**
 * 交易记录删除组件实现
 *
 * @since 1.0.0
 */
@Component("transInfoDeleteComponent")
@Getter
public class TransInfoDeleteComponentImpl implements
        TransInfoDeleteComponent,
        DaoBasedSingleDelete<Long, TransInfoDeleteParam, TransInfoDeleteArg> {

    private final TransInfoDao dao;
    private final TransInfoConverter converter;

    public TransInfoDeleteComponentImpl(TransInfoDao dao, TransInfoConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }
} 