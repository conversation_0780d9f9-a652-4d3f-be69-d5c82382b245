package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 默认收款方式枚举
 * 定义是否为售票员默认收款方式
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum PayDefaultEnum {
    YES("T", "是"),
    NO("F", "否");

    @JsonValue
    private final String code;
    private final String description;

    PayDefaultEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    // --- 高效反向查找机制 ---
    private static final Map<String, PayDefaultEnum> CODE_MAP = 
        Arrays.stream(values()).collect(Collectors.toMap(PayDefaultEnum::getCode, Function.identity()));

    /**
     * 根据 code 获取枚举实例
     *
     * @param code 默认收款方式编码
     * @return 对应的枚举实例，如果找不到则返回 null
     */
    @JsonCreator
    public static PayDefaultEnum ofCode(String code) {
        if (code == null) {
            return null;
        }
        return CODE_MAP.get(code);
    }
} 