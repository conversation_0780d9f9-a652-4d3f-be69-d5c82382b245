package com.sendinfo.ticketing.verification.basic.component.infra;

import java.util.List;

import com.sendinfo.ticketing.verification.basic.component.infra.param.PwClientMenuQueryParam;
import com.sendinfo.ticketing.verification.basic.model.infra.PwClientMenu;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

/**
 * 客户端菜单查询组件
 *
 * <AUTHOR> Generated 2025-07-25
 */
public interface PwClientMenuReadComponent extends ReadComponent<Long, PwClientMenuQueryParam, PwClientMenu> {

    /**
     * 根据父ID列表查询客户端菜单列表
     *
     * @param parentIds 父ID列表
     * @return 客户端菜单列表
     */
    List<PwClientMenu> queryByParentIds(List<Long> parentIds);

    /**
     * 查询已征用的客户端菜单列表
     *
     * @param corpCode 公司代码
     * @return 已征用的客户端菜单列表
     */
    List<PwClientMenu> queryHasRequisitionList(String corpCode);
}
