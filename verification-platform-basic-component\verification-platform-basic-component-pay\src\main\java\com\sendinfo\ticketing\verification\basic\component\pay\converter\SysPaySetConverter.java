package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.SysPaySetMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysPaySetUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysPaySet;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysPaySetUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysPaySetDO;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付标签转换器
 * 实现各种对象间的转换逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysPaySetConverter")
public class SysPaySetConverter implements
        CreateParam2DoConverter<SysPaySetCreateParam, SysPaySetDO>,
        ReadDo2ModelConverter<SysPaySetDO, SysPaySet>,
        ReadParam2ArgConverter<SysPaySetQueryParam, SysPaySetQueryArg>,
        UpdateParam2ArgConverter<SysPaySetUpdateParam, SysPaySetUpdateArg, SysPaySet> {

    private final SysPaySetMapper mapper = SysPaySetMapper.INSTANCE;

    @Override
    public SysPaySetDO c_p2d(SysPaySetCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        SysPaySetDO dataObject = mapper.convert(createParam);
        // 可以在此添加MapStruct难以处理的额外逻辑
        return dataObject;
    }

    @Override
    public SysPaySet r_d2m(SysPaySetDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<SysPaySet> r_ds2ms(List<SysPaySetDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public SysPaySetQueryArg r_p2a(SysPaySetQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        SysPaySetQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }

    @Override
    public SysPaySetUpdateArg u_p2a(SysPaySetUpdateParam updateParam, SysPaySet currentModel) {
        if (updateParam == null) {
            return null;
        }
        SysPaySetUpdateArg updateArg = mapper.convert(updateParam);
        return updateArg;
    }
} 