package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.SysMainbodyManageMmSubMerchantsMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyManageMmSubMerchantsUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbodyManageMmSubMerchants;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyManageMmSubMerchantsUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyManageMmSubMerchantsDO;
import com.sendinfo.ticketing.verification.common.component.converter.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 经营主体支付配置子商户关联表转换器
 * 实现各种对象间的转换逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyManageMmSubMerchantsConverter")
public class SysMainbodyManageMmSubMerchantsConverter implements
        CreateParam2DoConverter<SysMainbodyManageMmSubMerchantsCreateParam, SysMainbodyManageMmSubMerchantsDO>,
        ReadDo2ModelConverter<SysMainbodyManageMmSubMerchantsDO, SysMainbodyManageMmSubMerchants>,
        ReadParam2ArgConverter<SysMainbodyManageMmSubMerchantsQueryParam, SysMainbodyManageMmSubMerchantsQueryArg>,
        UpdateParam2ArgConverter<SysMainbodyManageMmSubMerchantsUpdateParam, SysMainbodyManageMmSubMerchantsUpdateArg, SysMainbodyManageMmSubMerchants> {

    private final SysMainbodyManageMmSubMerchantsMapper mapper = SysMainbodyManageMmSubMerchantsMapper.INSTANCE;

    @Override
    public SysMainbodyManageMmSubMerchantsDO c_p2d(SysMainbodyManageMmSubMerchantsCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        SysMainbodyManageMmSubMerchantsDO dataObject = mapper.convert(createParam);
        // 可以在此添加MapStruct难以处理的额外逻辑
        return dataObject;
    }

    @Override
    public SysMainbodyManageMmSubMerchants r_d2m(SysMainbodyManageMmSubMerchantsDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<SysMainbodyManageMmSubMerchants> r_ds2ms(List<SysMainbodyManageMmSubMerchantsDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public SysMainbodyManageMmSubMerchantsQueryArg r_p2a(SysMainbodyManageMmSubMerchantsQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        SysMainbodyManageMmSubMerchantsQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }

    @Override
    public SysMainbodyManageMmSubMerchantsUpdateArg u_p2a(SysMainbodyManageMmSubMerchantsUpdateParam updateParam, SysMainbodyManageMmSubMerchants currentModel) {
        if (updateParam == null) {
            return null;
        }
        SysMainbodyManageMmSubMerchantsUpdateArg updateArg = mapper.convert(updateParam);
        return updateArg;
    }
} 