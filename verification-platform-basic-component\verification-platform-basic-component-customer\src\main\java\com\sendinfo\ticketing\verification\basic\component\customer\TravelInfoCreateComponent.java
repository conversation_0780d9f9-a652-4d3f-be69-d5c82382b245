package com.sendinfo.ticketing.verification.basic.component.customer;

import com.sendinfo.ticketing.verification.basic.component.customer.param.TravelInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.model.customer.TravelInfo;
import com.sendinfo.ticketing.verification.common.component.CreateComponent;

/**
 * 旅行社信息读取组件接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface TravelInfoCreateComponent extends CreateComponent<TravelInfoCreateParam, Long> {

    /**
     * 创建旅行社信息
     *
     * @param createParam 创建参数
     * @return 旅行社信息
     */
    TravelInfo createTravelInfo(TravelInfoCreateParam createParam);
}