spring.application.name=verification-platform-basic
server.port=10010
nacos.config.server-addr=testnacos.sendinfocs.com:8848
nacos.config.namespace=bb13d33c-bda0-4552-91c3-173151d43186
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.druid.initial-size=20
spring.datasource.druid.max-active=100
spring.datasource.druid.min-idle=20
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
management.server.port=11000
management.endpoints.web.exposure.include=metrics,info,health,prometheus,shutdown
management.health.defaults.enabled=false
management.endpoint.shutdown.enabled=true
management.endpoint.health.show-details=NEVER
management.endpoint.health.group.readiness.include=ping
management.endpoint.health.group.liveness.include=ping
management.metrics.tags.application=${spring.application.name}
logging.file.path=/application/logs/${spring.application.name}
## jwt
verification.old-pw.jwt.secret=Xeg1
verification.old-pw.jwt.expiration-ms=43200000
## sendinfo paas rds config
sendinfo-paas-rds.client.app-name=verification-platform-basic
sendinfo-paas-rds.client.data-sources[0].rds-data-source-uri=verification_platform://data-source/verification_platform-dataSource
sendinfo-paas-rds.client.data-sources[0].bean-name=verificationDataSource
sendinfo-paas-rds.client.data-sources[0].primary=true
sendinfo-paas-rds.client.data-sources[1].rds-data-source-uri=saas_javapw_virtual://data-source/saas_javapw_virtual-dataSource
sendinfo-paas-rds.client.data-sources[1].bean-name=saasJavapwVirtualDataSource
sendinfo-paas-rds.client.data-sources[2].rds-data-source-uri=ticket_infra://data-source/ticket_infra-dataSource
sendinfo-paas-rds.client.data-sources[2].bean-name=ticketInfraDataSource

## rpc
verification-platform-basic.rpc.register.address=nacos://${nacos.config.server-addr}?namespace=${nacos.config.namespace}
## sendinfo paas cache config
sendinfo-paas-cache.client.app-name=verification-platform-basic
sendinfo-paas-cache.client.namespaces[0].name=verification-platform-basic-namespace
sendinfo-paas-cache.client.namespaces[0].bean-name=defaultCache
sendinfo-paas-cache.client.namespaces[1].name=sendinfo-business-server-namespace
sendinfo-paas-cache.client.namespaces[1].bean-name=oldPwCache
##
verification-platform-basic.nacos.tenant-datasource-mapping.data-id=saas-pw-datasource-sharding-mapping.json
verification-platform-basic.nacos.tenant-datasource-mapping.group=DEFAULT_GROUP
verification-platform-basic.nacos.tenant-datasource-mapping.pulled.timeout=3000
## sendinfo paas message config
sendinfo-paas-message.client.app-name=verification-platform-basic
sendinfo-paas-message.client.message-producer-uris[0].name=tgrp_rocket_canvas://topic/topic_verification-platform-basic
sendinfo-paas-message.client.message-producer-uris[0].bean-name=basicMessageProducer
sendinfo-paas-message.client.message-consumer-uris[0].name=tgrp_rocket_canvas://queue/queue_verification-platform-basic_ticket-binlog-sync
sendinfo-paas-message.client.message-consumer-uris[0].bean-name=goodsPropertyTicketSyncBinlogConsumer
sendinfo-paas-message.client.message-consumer-uris[1].name=tgrp_rocket_canvas://queue/queue_verification-platform-basic_userticketrelation-binlog-sync
sendinfo-paas-message.client.message-consumer-uris[1].bean-name=userTicketRelationSyncBinlogConsumer
