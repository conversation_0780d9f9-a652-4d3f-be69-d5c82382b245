package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktClassInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktClassInfo;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

public interface TktClassInfoReadComponent extends ReadComponent<Long, TktClassInfoQueryParam, TktClassInfo> {
    /**
     * 批量获取票型种类信息
     * @param classCodes
     * @param corpCode
     * @return
     */
    List<TktClassInfo> batchQueryClassInfoByClassCodes(List<String> classCodes,String corpCode);
} 