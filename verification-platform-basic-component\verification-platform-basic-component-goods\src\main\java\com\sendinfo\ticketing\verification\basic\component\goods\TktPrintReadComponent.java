package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktPrintQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktPrint;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 打印读取组件接口
 *
 * <AUTHOR>
 */
public interface TktPrintReadComponent extends ReadComponent<Long, TktPrintQueryParam, TktPrint> {

	/**
	 * 根据票码查询打印设置
	 *
	 * @param corpCode
	 * @param ticketCode
	 * @return
	 */
	TktPrint getTktPrintByTicketCode(String corpCode, String ticketCode);
	/**
	 * 批量查询票码打印设置
	 *
	 * @param ticketIds
	 * @param corpCode
	 * @return
	 */
	List<TktPrint> batchQueryTktPrintByTicketIds(List<Long> ticketIds, String corpCode);
} 