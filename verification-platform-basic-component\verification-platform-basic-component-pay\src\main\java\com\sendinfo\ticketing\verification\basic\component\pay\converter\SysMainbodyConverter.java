package com.sendinfo.ticketing.verification.basic.component.pay.converter;

import com.sendinfo.ticketing.verification.basic.component.pay.mapper.SysMainbodyMapper;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.SysMainbodyUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.SysMainbody;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.SysMainbodyUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.SysMainbodyDO;
import com.sendinfo.ticketing.verification.common.component.converter.CreateParam2DoConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import com.sendinfo.ticketing.verification.common.component.converter.UpdateParam2ArgConverter;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 经营主体转换器
 * 实现各种转换器接口，协调MapStruct Mapper完成对象转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("sysMainbodyConverter")
public class SysMainbodyConverter implements
        CreateParam2DoConverter<SysMainbodyCreateParam, SysMainbodyDO>,
        ReadDo2ModelConverter<SysMainbodyDO, SysMainbody>,
        ReadParam2ArgConverter<SysMainbodyQueryParam, SysMainbodyQueryArg>,
        UpdateParam2ArgConverter<SysMainbodyUpdateParam, SysMainbodyUpdateArg, SysMainbody> {

    private final SysMainbodyMapper mapper = SysMainbodyMapper.INSTANCE;

    @Override
    public SysMainbodyDO c_p2d(SysMainbodyCreateParam createParam) {
        if (createParam == null) {
            return null;
        }
        return mapper.convert(createParam);
    }

    @Override
    public SysMainbody r_d2m(SysMainbodyDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }

    @Override
    public List<SysMainbody> r_ds2ms(List<SysMainbodyDO> dataObjects) {
        if (dataObjects == null) {
            return null;
        }
        return dataObjects.stream().map(this::r_d2m).collect(Collectors.toList());
    }

    @Override
    public SysMainbodyQueryArg r_p2a(SysMainbodyQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        SysMainbodyQueryArg queryArg = mapper.convert(queryParam);

        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());

        return queryArg;
    }

    @Override
    public SysMainbodyUpdateArg u_p2a(SysMainbodyUpdateParam updateParam, SysMainbody currentModel) {
        if (updateParam == null) {
            return null;
        }
        return mapper.convert(updateParam);
    }
} 