package com.sendinfo.ticketing.verification.basic.api.system.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 票型角色查询请求参数
 * <AUTHOR>
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysAuditRoleQueryRequest implements Serializable {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 权限类型
     */
    private Integer roleType;

    /**
     * 子系统ID
     */
    private Integer subsystemId;

    /**
     * 启用状态 F:停用 T:启动
     */
    private String useFlag;
} 