package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.MainbodyChanleUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.MainbodyChanle;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.MainbodyChanleUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.MainbodyChanleDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 商户渠道配置映射接口
 * 使用MapStruct进行对象映射转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface MainbodyChanleMapper {

    MainbodyChanleMapper INSTANCE = Mappers.getMapper(MainbodyChanleMapper.class);

    /**
     * 创建参数转换为数据对象
     */
    MainbodyChanleDO convert(MainbodyChanleCreateParam createParam);

    /**
     * 数据对象转换为模型
     */
    MainbodyChanle convert(MainbodyChanleDO dataObject);

    /**
     * 查询参数转换为查询参数
     */
    MainbodyChanleQueryArg convert(MainbodyChanleQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     */
    MainbodyChanleUpdateArg convert(MainbodyChanleUpdateParam updateParam);
} 