package com.sendinfo.ticketing.verification.basic.model.goods.properties;

import com.sendinfo.ticketing.verification.common.model.properties.PropertyKeyDefinition;

public enum TicketCalendarPricePropertyKey implements PropertyKeyDefinition {
    TAGS("tags");

    private final String key;

    TicketCalendarPricePropertyKey(String key) {
        this.key = key;
    }


    @Override
    public String key() {
        return key;
    }
}
