package com.sendinfo.ticketing.verification.basic.model.pay.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 客户类型枚举
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public enum ClientTypeEnum {
    /**
     * 散客
     */
    INDIVIDUAL(1, "散客"),

    /**
     * 团队
     */
    GROUP(2, "团队");

    /**
     * 编码
     */
    @JsonValue
    private final Integer code;

    /**
     * 描述
     */
    private final String description;

    ClientTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    @JsonCreator
    public static ClientTypeEnum ofCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ClientTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("Unknown client type code: " + code);
    }
} 