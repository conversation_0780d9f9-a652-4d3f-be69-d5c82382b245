package com.sendinfo.ticketing.verification.basic.component.goods.mapper;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TicketCalendarPriceQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TicketCalendarPrice;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TicketCalendarPriceMapper {
    TicketCalendarPriceMapper INSTANCE = Mappers.getMapper(TicketCalendarPriceMapper.class);

    TicketCalendarPriceQueryArg convert(TicketCalendarPriceQueryParam param);

    TicketCalendarPrice convert(TicketCalendarPriceDO ticketCalendarPriceDO);
}
