package com.sendinfo.ticketing.verification.basic.repository.pay.arg;

import com.sendinfo.ticketing.verification.common.repository.arg.AbstractDeleteArg;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 经营主体子商户删除参数
 * 用于封装sys_mainbody_manage_sub_merchants表的删除条件，支持租户隔离和软删除
 * 仅支持软删除操作，不提供物理删除功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysMainbodyManageSubMerchantsDeleteArg extends AbstractDeleteArg {
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 修改人
     */
    private String modifyBy;
} 