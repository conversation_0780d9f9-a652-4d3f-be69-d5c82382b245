package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.system.DepartmentInfoReadService;
import com.sendinfo.ticketing.verification.basic.model.system.DepartmentInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.Assert.*;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/14 10:46
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class DepartmentInfoReadServiceImplTest {

    @Autowired
    private DepartmentInfoReadService departmentInfoReadService;

//    @Test
    public void testQueryByParentIdSuccess() {

        ResultModel<List<DepartmentInfo>> resultModel = departmentInfoReadService.queryByParentId(16L, "0001");

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertFalse(resultModel.getModel().isEmpty());
    }

}
