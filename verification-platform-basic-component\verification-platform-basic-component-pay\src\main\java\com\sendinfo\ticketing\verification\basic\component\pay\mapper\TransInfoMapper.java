package com.sendinfo.ticketing.verification.basic.component.pay.mapper;

import com.sendinfo.ticketing.verification.basic.api.pay.response.TransInfoPayAwayResponse;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoCreateParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoDeleteParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.component.pay.param.TransInfoUpdateParam;
import com.sendinfo.ticketing.verification.basic.model.pay.TransInfo;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.BusinessTypeEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayStatusEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.TransTypeEnum;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoDeleteArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.arg.TransInfoUpdateArg;
import com.sendinfo.ticketing.verification.basic.repository.pay.dataobject.TransInfoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 售票员收款方式映射接口
 * 使用MapStruct进行对象映射转换
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(
        imports = {BusinessTypeEnum.class, TransTypeEnum.class, PayStatusEnum.class}
)
public interface TransInfoMapper {

    TransInfoMapper INSTANCE = Mappers.getMapper(TransInfoMapper.class);

    /**
     * 创建参数转换为数据对象
     *
     * @param createParam 创建参数
     * @return 数据对象
     */
    @Mapping(target = "transType", expression = "java(createParam.getTransType() != null ? createParam.getTransType().getCode() : null)")
    @Mapping(target = "businessType", expression = "java(createParam.getBusinessType() != null ? createParam.getBusinessType().getCode() : null)")
    @Mapping(target = "payStatus", expression = "java(createParam.getPayStatus() != null ? createParam.getPayStatus().getCode() : null)")
    TransInfoDO convert(TransInfoCreateParam createParam);

    /**
     * 数据对象转换为模型
     *
     * @param dataObject 数据对象
     * @return 模型
     */
    @Mapping(target = "transType", expression = "java(TransTypeEnum.ofCode(dataObject.getTransType()))")
    @Mapping(target = "businessType", expression = "java(BusinessTypeEnum.ofCode(dataObject.getBusinessType()))")
    @Mapping(target = "payStatus", expression = "java(PayStatusEnum.ofCode(dataObject.getPayStatus()))")
    TransInfo convert(TransInfoDO dataObject);

    /**
     * 查询参数转换为查询参数
     *
     * @param queryParam 查询参数
     * @return 查询参数
     */
    @Mapping(target = "transType", expression = "java(queryParam.getTransType() != null ? queryParam.getTransType().getCode() : null)")
    @Mapping(target = "businessType", expression = "java(queryParam.getBusinessType() != null ? queryParam.getBusinessType().getCode() : null)")
    @Mapping(target = "payStatus", expression = "java(queryParam.getPayStatus() != null ? queryParam.getPayStatus().getCode() : null)")
    TransInfoQueryArg convert(TransInfoQueryParam queryParam);

    /**
     * 更新参数转换为更新参数
     *
     * @param updateParam  更新参数
     * @return 更新参数
     */
    @Mapping(target = "payStatus", expression = "java(updateParam.getPayStatus() != null ? updateParam.getPayStatus().getCode() : null)")
    TransInfoUpdateArg convert(TransInfoUpdateParam updateParam);

    TransInfoDeleteArg convert(TransInfoDeleteParam deleteParam);

    /**
     * 模型转换为响应结果
     *
     * @param transInfo 模型
     * @return 响应结果
     */
    @Mapping(target = "transType", expression = "java(transInfo.getTransType() != null ? transInfo.getTransType().getCode() : null)")
    @Mapping(target = "businessType", expression = "java(transInfo.getBusinessType() != null ? String.valueOf(transInfo.getBusinessType().getCode()) : null)")
    @Mapping(target = "payStatus", expression = "java(transInfo.getPayStatus() != null ? transInfo.getPayStatus().getCode() : null)")
    TransInfoPayAwayResponse convert(TransInfo transInfo);
}