package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.system.request.SellerUserInfoQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.system.UserInfoReadService;
import com.sendinfo.ticketing.verification.basic.model.system.UserInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/14 10:46
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class UserInfoReadServiceImplTest {

    @Autowired
    private UserInfoReadService userInfoReadService;

//    @Test
    public void testQueryByIdSuccess() {

        ResultModel<UserInfo> resultModel = userInfoReadService.queryById(1L);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertNotNull(resultModel.getModel());
    }

//    @Test
    public void testQueryListBySellerUserInfoSuccess() {
        SellerUserInfoQueryRequest request = new SellerUserInfoQueryRequest();
        request.setRealName("平台管理员");
        request.setDeptIdSet(Set.of(16L, 108L));
        request.setCorpCode("0001");
        request.setUserId(2L);
        ResultModel<List<UserInfo>> resultModel = userInfoReadService.queryListBySellerUserInfo(request);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
        assertFalse(resultModel.getModel().isEmpty());
    }
}
