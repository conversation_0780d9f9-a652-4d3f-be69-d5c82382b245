package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商户渠道配置创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class MainbodyChanleCreateParam extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付渠道ID
     */
    private Long payChanleId;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 商户ID
     */
    private Long merchantId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String modifyBy;
} 