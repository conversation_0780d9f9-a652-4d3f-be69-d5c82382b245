package com.sendinfo.ticketing.verification.basic.model.goods;

import com.sendinfo.ticketing.verification.common.model.properties.AbstractProperties;
import com.sendinfo.ticketing.verification.basic.model.goods.properties.TicketCalendarPricePropertyKey;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025/7/26 17:33
 **/
@Getter
@Setter
@ToString(callSuper = true)
public class TicketCalendarPrice extends AbstractProperties<TicketCalendarPricePropertyKey> {
    private static final long serialVersionUID = 7312099064731821632L;
    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 票型ID
     */
    private Long ticketId;

    /**
     * 开始时间范围查询
     */
    private LocalDateTime beginTimeStart;
    private LocalDateTime beginTimeEnd;

    /**
     * 结束时间范围查询
     */
    private LocalDateTime endTimeStart;
    private LocalDateTime endTimeEnd;

    /**
     * 星期几查询
     */
    private String weeDay;

    /**
     * create by
     */
    private String createBy;

    /**
     * modified by
     */
    private String modifyBy;
}
