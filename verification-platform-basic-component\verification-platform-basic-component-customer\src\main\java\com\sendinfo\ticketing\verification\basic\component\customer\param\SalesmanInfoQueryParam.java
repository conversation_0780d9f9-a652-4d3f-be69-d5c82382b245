package com.sendinfo.ticketing.verification.basic.component.customer.param;

import com.sendinfo.ticketing.verification.common.component.param.AbstractPageQueryParam;
import lombok.Getter;
import lombok.Setter;

/**
 * 业务员信息查询参数
 * 用于封装查询业务员信息的条件
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class SalesmanInfoQueryParam extends AbstractPageQueryParam {

    /**
     * 业务员名称
     */
    private String salesmanName;

    /**
     * 业务员简称
     */
    private String salesmanAbbreviation;

    /**
     * 禁启状态:T启用F禁用
     */
    private String useFlag;

    /**
     * 0是业务员1是推广员
     */
    private Integer salesmanType;

    /**
     * 企业编码
     */
    private String corpCode;
} 