package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.sendinfo.ticketing.verification.basic.api.customer.request.SalesmanInfoQueryEnabledByIdsRequest;
import com.sendinfo.ticketing.verification.basic.api.customer.SalesmanInfoReadService;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanInfo;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Set;

import static org.junit.Assert.*;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/14 10:46
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class SalesmanInfoReadServiceImplTest {

    @Autowired
    private SalesmanInfoReadService salesmanInfoReadService;

//    @Test
    public void testQueryEnabledByIdsSuccess() {

        SalesmanInfoQueryEnabledByIdsRequest request = new SalesmanInfoQueryEnabledByIdsRequest();
        request.setCorpCode("0001");
        request.setIds(Set.of(9L, 10L, 11L));

        ResultModel<List<SalesmanInfo>> resultModel = salesmanInfoReadService.queryEnabledByIds(request);

        assertNotNull(resultModel);
        assertTrue(resultModel.isSuccess());
    }

}
