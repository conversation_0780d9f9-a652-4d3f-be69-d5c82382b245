package com.sendinfo.ticketing.verification.basic.component.goods;

import com.sendinfo.ticketing.verification.basic.component.goods.param.TktTicketTypeQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TktTicketType;
import com.sendinfo.ticketing.verification.common.component.ReadComponent;

import java.util.List;

/**
 * 门票类型组件
 *
 * <AUTHOR>
 */
public interface TktTicketTypeReadComponent extends ReadComponent<Long, TktTicketTypeQueryParam, TktTicketType> {

	/**
	 * 根据id查询门票类型
	 *
	 * @param id    主键id
	 * @param corpCode 企业编码
	 * @return  TktTicketType
	 */
    TktTicketType getTktTicketTypeById(Long id, String corpCode);
	/**
	 * 根据id批量查询门票类型
	 *
	 * @param ids
	 * @return
	 */
	List<TktTicketType> batchQueryTicketTypeByIds(List<Long> ids, String corpCode);
} 