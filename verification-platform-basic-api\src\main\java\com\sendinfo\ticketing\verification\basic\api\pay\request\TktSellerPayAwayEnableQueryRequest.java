package com.sendinfo.ticketing.verification.basic.api.pay.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: chengjie.li
 * @Date: 2025/7/25 14:11
 * 售票员可用的支付方式查询参数
 */
@Getter
@Setter
public class TktSellerPayAwayEnableQueryRequest implements Serializable {

    private static final long serialVersionUID = -4976677549149847816L;

    /**
     * 售票员ID
     */
    @NotNull
    private Long accId;

    /**
     * 客户类型（1：散客 2：团队）
     */
    @NotNull
    private Integer clientType;

    /**
     * 售票模式：1:正常出票 2:预售票 3:电子商务票 4:手工票补录 5:剧院售票6：自助机 7:扫码入园
     */
    @NotNull
    private Integer saleModel;

    /**
     * 企业编码
     */
    @NotNull
    private String corpCode;

}
