package com.sendinfo.ticketing.verification.basic.component.goods.impl;

import com.sendinfo.ticketing.verification.basic.component.goods.TicketCalendarPriceReadComponent;
import com.sendinfo.ticketing.verification.basic.component.goods.converter.TicketCalendarPriceConverter;
import com.sendinfo.ticketing.verification.basic.component.goods.param.TicketCalendarPriceQueryParam;
import com.sendinfo.ticketing.verification.basic.model.goods.TicketCalendarPrice;
import com.sendinfo.ticketing.verification.basic.repository.goods.arg.TicketCalendarPriceQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.goods.dao.TicketCalendarPriceDao;
import com.sendinfo.ticketing.verification.basic.repository.goods.dataobject.TicketCalendarPriceDO;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedCountRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedListRead;
import com.sendinfo.ticketing.verification.common.component.support.DaoBasedSingleRead;
import lombok.Getter;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @since 2025/7/26 17:38
 **/
@Getter
@Component("ticketCalendarPriceReadComponent")
public class TicketCalendarPriceReadComponentImpl implements TicketCalendarPriceReadComponent,
        DaoBasedSingleRead<Long, TicketCalendarPriceQueryParam, TicketCalendarPrice, TicketCalendarPriceDO>,
        DaoBasedCountRead<Long, TicketCalendarPriceQueryParam, TicketCalendarPrice, TicketCalendarPriceQueryArg>,
        DaoBasedListRead<Long, TicketCalendarPriceQueryParam, TicketCalendarPrice, TicketCalendarPriceDO, TicketCalendarPriceQueryArg> {

    private final TicketCalendarPriceDao dao;
    private final TicketCalendarPriceConverter converter;

    public TicketCalendarPriceReadComponentImpl(TicketCalendarPriceDao dao, TicketCalendarPriceConverter converter) {
        this.dao = dao;
        this.converter = converter;
    }

    @Override
    public List<TicketCalendarPrice> batchQueryByTicketIds(List<Long> ticketIds, String corpCode) {
        List<TicketCalendarPriceDO> ticketCalendarPriceDOS = dao.batchQueryByTicketIds(ticketIds, corpCode);
        return Optional.ofNullable(ticketCalendarPriceDOS).map(converter::r_ds2ms).orElse(Collections.emptyList());
    }


}
