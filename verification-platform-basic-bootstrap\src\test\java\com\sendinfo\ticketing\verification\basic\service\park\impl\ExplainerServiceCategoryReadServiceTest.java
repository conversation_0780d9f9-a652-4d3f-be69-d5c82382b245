package com.sendinfo.ticketing.verification.basic.service.park.impl;

import com.alibaba.fastjson.JSON;
import com.sendinfo.ticketing.verification.basic.api.goods.request.ExplainerServiceCategoryQueryRequest;
import com.sendinfo.ticketing.verification.basic.api.goods.ExplainerServiceCategoryReadService;
import com.sendinfo.ticketing.verification.basic.model.goods.ExplainerServiceCategory;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 讲解员信息读取服务测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/7 15:17
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = VerificationPlatformBasicBootstrap.class)
public class ExplainerServiceCategoryReadServiceTest {

	@Autowired
	private ExplainerServiceCategoryReadService explainerServiceCategoryReadService;

//	@Test
	public void queryExplainerListTest() {

		// 构造请求参数
		ExplainerServiceCategoryQueryRequest request = new ExplainerServiceCategoryQueryRequest();
		request.setCorpCode("0001");
		request.setUseFlag("T");

		// 调用远程服务
		try {
			ResultModel<List<ExplainerServiceCategory>> resultModel =
					explainerServiceCategoryReadService.queryServiceCategoryList(request);
			if (resultModel.isSuccess()) {
				System.out.println(JSON.toJSONString(resultModel.getModel()));
			}
		} catch (Exception e) {
			System.err.println("调用失败: " + e.getMessage());
			e.printStackTrace();
		}
	}
}
