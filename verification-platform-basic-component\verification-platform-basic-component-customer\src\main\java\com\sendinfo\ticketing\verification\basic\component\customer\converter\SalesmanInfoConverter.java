package com.sendinfo.ticketing.verification.basic.component.customer.converter;

import com.sendinfo.ticketing.verification.basic.component.customer.mapper.SalesmanInfoMapper;
import com.sendinfo.ticketing.verification.basic.component.customer.param.SalesmanInfoQueryParam;
import com.sendinfo.ticketing.verification.basic.model.customer.SalesmanInfo;
import com.sendinfo.ticketing.verification.basic.repository.customer.arg.SalesmanInfoQueryArg;
import com.sendinfo.ticketing.verification.basic.repository.customer.dataobject.SalesmanInfoDO;
import com.sendinfo.ticketing.verification.common.component.converter.ReadDo2ModelConverter;
import com.sendinfo.ticketing.verification.common.component.converter.ReadParam2ArgConverter;
import org.springframework.stereotype.Component;

/**
 * 业务员信息转换器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component("salesmanInfoConverter")
public class SalesmanInfoConverter implements ReadDo2ModelConverter<SalesmanInfoDO, SalesmanInfo>,
        ReadParam2ArgConverter<SalesmanInfoQueryParam, SalesmanInfoQueryArg> {

    private final SalesmanInfoMapper mapper = SalesmanInfoMapper.INSTANCE;

    // DO -> Model
    @Override
    public SalesmanInfo r_d2m(SalesmanInfoDO dataObject) {
        if (dataObject == null) {
            return null;
        }
        return mapper.convert(dataObject);
    }


    // QueryParam -> QueryArg
    @Override
    public SalesmanInfoQueryArg r_p2a(SalesmanInfoQueryParam queryParam) {
        if (queryParam == null) {
            return null;
        }
        SalesmanInfoQueryArg queryArg = mapper.convert(queryParam);
        // 处理分页参数
        queryArg.setOffset(queryParam.getStartIndex());
        queryArg.setLimit(queryParam.getPageSize());
        return queryArg;
    }
} 