package com.sendinfo.ticketing.verification.basic.component.pay.param;

import com.sendinfo.ticketing.verification.basic.enums.CommonUseFlagEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayDefaultEnum;
import com.sendinfo.ticketing.verification.basic.model.pay.enums.PayTypeEnum;
import com.sendinfo.ticketing.verification.common.component.param.AbstractCreateParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 支付标签创建参数
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
@ToString(callSuper = true)
public class SysPaySetCreateParam extends AbstractCreateParam {

    /**
     * 企业编码
     */
    private String corpCode;

    /**
     * 支付标签名称
     */
    private String paylableName;

    /**
     * 支付平台编码
     */
    private String payTypeCode;

    /**
     * 支付标签图标
     */
    private String paylableIcon;

    /**
     * 启用状态
     */
    private CommonUseFlagEnum useFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 售票员默认收款方式
     */
    private PayDefaultEnum payDefault;

    /**
     * 主体ID
     */
    private Long mainbodyId;

    /**
     * 支付类型（主扫，被扫，现金）
     */
    private PayTypeEnum payType;

    /**
     * 自定义支付名称
     */
    private String payName;

    /**
     * 迁移数据
     */
    private String transferParam;

    /**
     * create by
     */
    private String createBy;

    /**
     * modify by
     */
    private String modifyBy;
} 