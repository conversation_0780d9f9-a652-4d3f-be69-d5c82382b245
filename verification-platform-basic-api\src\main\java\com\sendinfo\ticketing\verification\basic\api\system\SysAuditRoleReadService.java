package com.sendinfo.ticketing.verification.basic.api.system;

import com.sendinfo.ticketing.verification.basic.api.system.request.SysAuditRoleQueryRequest;
import com.sendinfo.ticketing.verification.basic.model.system.SysAuditRole;
import com.sendinfo.ticketing.verification.common.api.result.ResultModel;

import java.util.List;
import java.util.Set;

/**
 * 票型角色读取服务接口
 * <AUTHOR>
 */
public interface SysAuditRoleReadService {

    /**
     * 查询票型角色列表
     *
     * @param request 查询条件
     * @return 票型角色列表
     */
    ResultModel<List<SysAuditRole>> querySysAuditRoles(SysAuditRoleQueryRequest request);

    /**
     * 查询票型角色列表
     *
     * @param ids ids集合
     * @param corpCode 企业编码
     * @return 票型角色列表
     */
    ResultModel<List<SysAuditRole>> querySysAuditRoleByIds(Set<Integer> ids, String corpCode);
} 