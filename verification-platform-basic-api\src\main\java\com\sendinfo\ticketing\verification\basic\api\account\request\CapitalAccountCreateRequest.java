package com.sendinfo.ticketing.verification.basic.api.account.request;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 资金账户创建请求
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@Setter
public class CapitalAccountCreateRequest implements Serializable {

    /**
     * 企业编码
     */
    private String corpCode;
    /**
     * 旅行社ID
     */
    private Long travelId;
    /**
     * 账户名称
     */
    private String name;
    /**
     * 账户余额
     */
    private BigDecimal leftValue;
    /**
     * 备用金支付密码（预留备用）
     */
    private String capitalPass;
    /**
     * 总信用额度（预留备用）
     */
    private BigDecimal creditValue;
    /**
     * 可用信用额度（预留备用）
     */
    private BigDecimal creditLeftValue;
    /**
     * 最近充值时间
     */
    private Date lastRechargingTime;
    /**
     * 账户状态 0:停用 1:启用 2:未激活
     */
    private Integer status;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 迁移数据
     */
    private String transferParam;
} 